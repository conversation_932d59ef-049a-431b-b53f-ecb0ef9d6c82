import { SilentReAsk } from './silent_requestion'
import { Config } from 'config'
import { loadConfigByAccountName } from '../database/config'
import { DateHelper } from 'lib/date/date'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('haogu1')

    const delayed = await SilentReAsk.getQueue().getDelayed()

    delayed.filter((delay) => delay.data.chat_id === '7114915486515201054_2300').map((item) => {
      // console.log(JSON.stringify(item.data, null, 4))
      // if ()
      if (item.data.task_name === 'big_plan') {
        console.log(DateHelper.msToTime(item.data.waiting_time))
      }

    })

  })
})