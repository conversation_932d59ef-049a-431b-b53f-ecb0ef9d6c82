import { MessageReplyService } from '../reply/message_reply'
import { Que<PERSON>, Worker } from 'bullmq'
import { LRUCache } from 'lru-cache'
import { Config } from 'config'
import { getBotId, getChatId, getUserId } from 'config/chat_id'
import { RedisCacheDB } from 'model/redis/redis_cache'
import logger from 'model/logger/logger'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { sleep } from 'lib/schedule/schedule'
import { DateHelper } from 'lib/date/date'
import { JuziAPI } from 'model/juzi/api'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { ChatInterruptHandler } from '../interrupt/interrupt_handler'
import { ChatStateStore } from '../../local_cache/chat_state_store'
import { ChatDB, IChat } from '../../database/chat'

import { CloudCustomData, ScrmMessageToAiStaff, ScrmMessageType } from 'model/haogu/callback/type'
import XunfeiASR from 'model/nls/xunfei'

export interface ImageAndText {
  text?: string
  imageUrl?: string
}

interface IMessageHandlers {
  handleClassGroupMessage?: (message: ScrmMessageToAiStaff) =>  Promise<any> // 默认不处理
  handleUnknownMessage: (message: ScrmMessageToAiStaff) => Promise<any>

  handleImageMessage(imageUrl: string, chatId: string): Promise<string> // 返回图片转文字结果
  handleVideoMessage(videoUrl: string, chatId: string): Promise<string> // 返回视频转文字结果

  sendWelcomeMessage(chatId: string, userId: string): Promise<void>

  handleTextAndImageMessage?: (imageAndText: ImageAndText[], chatId: string, userId: string) => Promise<string> // 如果有关于图片的问题，对图文消息进行合并，走LLM多模态分析，直接返回问题回答给客户
  imageWaitingTime?: number // 修改图片单独等待时间， 单位 毫秒
}

/**
 * 用于管理全局消息队列，为每个客户设置单独的计时器，合并处理消息
 */
export class HaoguMessageHandler {
  private readonly handlers: IMessageHandlers
  private readonly messageSet = new LRUCache<string, any>({ max: 3000 })
  private _messageQueueBullMQ: Queue
  private messageQueueWorker?: Worker
  private chatDBClient:ChatDB<IChat>
  private chatHistoryServiceClient:ChatHistoryService
  private chatStateStoreClient:ChatStateStore
  private messageReplyServiceClient:MessageReplyService
  private eventTrackClient:EventTracker

  constructor(handlers:IMessageHandlers, chatDBClient:ChatDB<IChat>, chatHistoryServiceClient:ChatHistoryService, chatStateStoreClient:ChatStateStore, messageReplyServiceClient:MessageReplyService, eventTrackClient:EventTracker) {
    this.handlers = handlers
    this.chatDBClient = chatDBClient
    this.chatHistoryServiceClient = chatHistoryServiceClient
    this.chatStateStoreClient = chatStateStoreClient
    this.messageReplyServiceClient = messageReplyServiceClient
    this.eventTrackClient = eventTrackClient
  }

  private get messageQueueBullMQ():Queue {
    if (!this._messageQueueBullMQ) {
      this._messageQueueBullMQ = new Queue(this.queueName, {
        connection: RedisCacheDB.getInstance()
      })
    }
    return this._messageQueueBullMQ
  }

  private getMessageStoreName(chatId: string) {

    return `user-message-store-${Config.setting.projectName}-${chatId}`
  }

  private get queueName() {
    return `user-message-queue-${Config.setting.projectName}`
  }

  public async handle (message: ScrmMessageToAiStaff & {chat_id:string}) {
    if (!message.chat_id) {
      return
    }

    const messageId = message.serverId
    if (!messageId) {
      return
    }

    if (messageId && this.messageSet.has(messageId)) {
      return
    }

    // 不处理群消息
    if (message.isRoom) {
      return
    }

    if (message.type == ScrmMessageType.text && !message.isStaff) {
      logger.debug('接收消息', message.content, message.chat_id)
      // 文本消息，提前 increment 一下 version 来即时打断
      await ChatInterruptHandler.incrementChatVersion(message.chat_id)
    }

    this.messageSet.set(messageId, 1)

    try {
      const messageStore = new RedisCacheDB(this.getMessageStoreName(message.chat_id)) // 改为 message_store + chatId 来存储，否则可能有 key 冲突问题
      // 将消息存到 Redis
      await messageStore.addSet(JSON.stringify(message))

      let delay =  Config.setting.waitingTime.messageMerge
      if (Config.isTestAccount(getBotId(message.chat_id))) {
        delay = 5 * 1000
      } else if (message.type === ScrmMessageType.image) { // 单独设置 图片等待时长
        delay = this.handlers.imageWaitingTime ? this.handlers.imageWaitingTime : delay
      }

      // 添加消息，到消息队列
      await this.messageQueueBullMQ.add(message.chat_id, { chatId: message.chat_id, messageId }, { delay, removeOnComplete: true, removeOnFail: true })
    } catch (e) {
      logger.error('消息添加到任务队列失败', e)
    }
  }

  public startWorker() {
    if (!this.messageQueueWorker) {
      this.messageQueueWorker = new Worker(this.queueName, async (job) => {
        const { chatId, messageId } = job.data

        // 只处理最新的消息
        const isLatest = await this.isLatestMessage(chatId, messageId)
        if (!isLatest) {
          logger.debug(`跳过非最新消息: ${messageId}`)
          return
        }

        // 如果最后一条消息 是 AI 消息，并跟现在的时间比较接近，则做下延迟处理，拖延一下回复速度
        const messages =  await this.chatHistoryServiceClient.getChatHistoryByChatId(chatId)
        if (messages.length > 0) {
          const lastAIMessageReplyDiff = DateHelper.diff(messages[messages.length - 1].created_at, new Date(), 'second')
          if (lastAIMessageReplyDiff < 5) {
            await sleep(5 * 1000)
          }
        }

        await this.processUserMessages(chatId)
      }, { connection: RedisCacheDB.getInstance(), concurrency: 20 })

      this.messageQueueWorker.on('error', (error) => {
        logger.error('消息处理 Worker error:', error)
      })
    }
  }

  private async isLatestMessage(chatId: string, messageId: string) {
    const messageStore = new RedisCacheDB(this.getMessageStoreName(chatId))
    const messages = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      return false
    }

    // 消息有可能不是按顺序接收的，需要按时间重排序下
    messages.sort((msgA, msgB) => {
      return msgA.msgTime - msgB.msgTime
    })

    return messages[messages.length - 1].serverId === messageId
  }

  public async processUserMessages (chatId: string) {
    try {
      // 获取该客户在这段时间内发送的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreName(chatId))
      const userMessages = await messageStore.getSetMembers()

      // 消息有可能不是按顺序接收的，需要按时间重排序下
      userMessages.sort((msgA, msgB) => {
        return msgA.msgTime - msgB.msgTime
      })

      // 从消息队列中移除已处理的消息
      await messageStore.del()

      // 将消息转为文本
      const texts = await this.getTextsFromMessages (userMessages, chatId)
      if (texts.length == 0) return

      await this.messageReplyServiceClient.reply(texts, getUserId(chatId))
    } catch (e) {
      console.error (`处理客户 ${chatId} 的消息出错：`, e)
    }
  }

  private async getTextsFromMessages(messages: ScrmMessageToAiStaff[], chatId: string): Promise<string[]> {
    const texts : string[] = []

    const requireMultiModal = Boolean(this.handlers.handleTextAndImageMessage) // 目前只有好人好股需要这个功能
    const imageAndText: ImageAndText[] = []


    for (const message of messages) {
      try {
        if (message.serverId) {
          const cache = new RedisCacheDB(message.serverId)
          await cache.set(message, 3 * 60 * 60) // 缓存 3小时
        }
      } catch (e) {
        logger.warn('缓存消息失败')
      }


      try {
        let text: string = ''

        // 忽略欢迎语
        if (message.type === ScrmMessageType.text && (HaoguMessageHandler.isWelcomeMessage(message.content as string) || await this.isFirstMessage(chatId))) {
          const flags = await this.chatStateStoreClient.getFlags<any>(chatId)
          if (flags.is_friend_accepted) {
            continue
          }

          // 如果没有 欢迎语的话，进入欢迎语流程
          await this.handlers.sendWelcomeMessage(chatId, getUserId(chatId))
          continue
        }

        switch (message.type) {
          case ScrmMessageType.text: {
            text = message.content ?? ''

            // if (payload.quoteMessage) {
            //   if (!payload.quoteMessage.content.text) { // 引用非文本，查一下 对应的附件的注释
            //     // 查询一下 messageId 对应的 chatHistory
            //     const chatHistory = await this.chatHistoryServiceClient.getMessageByMessageId(payload.quoteMessage.messageId)

            //     if (chatHistory && chatHistory.short_description) {
            //       text = `对“${chatHistory.short_description}”的回复是：\n${payload.text}`
            //     } else {
            //       text =  `${payload.text}`
            //     }

            //   } else { // 引用文本回复
            //     text = `对“${payload.quoteMessage.content.text}”的回复是：\n${payload.text}`
            //   }
            // }
            break
          }

          case ScrmMessageType.emotion || ScrmMessageType.emotionPacket:
            text = '【表情】'
            break

          case ScrmMessageType.voice: {
            // const msg = message.payload as IReceivedVoiceMsg
            // if (msg.text) {
            //   text = msg.text
            // } else {
            const xunfei = new XunfeiASR({
              appId: Config.setting.xunfeiASR.appId,
              secretKey: Config.setting.xunfeiASR.secretKey,
              uploadFileUrl: message.filePath
            })
            text = await xunfei.getResult()
            // }
            break
          }

          case ScrmMessageType.image: {
            // 当isHandleImageAndText为true（好人好股）时，只把图片放在imageAndText数组中，返回的text为空字符串。
            text = await this.handleImageMessage(chatId, message, requireMultiModal,  imageAndText) ?? text
            break
          }

          case ScrmMessageType.video: {
            text = await this.handleVideoMessage(chatId, message) ?? text
            break
          }

          default:
            logger.log(JSON.stringify(message, null, 4))
            await this.handleUnknownMessageType(message, chatId)
        }

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
        if (message.isStaff) {
          if (!message.cloudCustomData) {
            continue
          }
          const cloudCustomData = JSON.parse(message.cloudCustomData) as CloudCustomData
          if (cloudCustomData.fromAi) {
            //正常ai回复的消息
            continue
          }
          logger.log('人工回复', JSON.stringify(message, null, 4))
          // 添加埋点，客户 + AI 回复
          if (text) {
            await this.chatHistoryServiceClient.addBotMessage(chatId, text, undefined, { is_send_by_human:true, message_id: message.messageId })
            this.eventTrackClient.track(chatId, IEventType.ManualReply, { reply: text, message: message.source })
          }
        } else {
          if (requireMultiModal) {
            imageAndText.push({
              text
            })
          }

          texts.push(text)
        }
      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        console.error('单条消息解析出错：', e)
      }
    }

    if (requireMultiModal && this.handlers.handleTextAndImageMessage && imageAndText.some((e) => Boolean(e.imageUrl))) {
      const analysisResult = await this.handlers.handleTextAndImageMessage(imageAndText, chatId, getUserId(chatId))
      if (analysisResult && analysisResult.trim().length > 0) {
        return [analysisResult]
      }
      return []
    }
    logger.debug('解析出的消息', texts)

    return texts
  }

  public async handleUnknownMessageType(message: ScrmMessageToAiStaff, chatId:string) {
    logger.log(chatId, '非文本消息类型', message.messageType)

    if (await this.chatDBClient.isHumanInvolvement(chatId)) { // 已经人工参与了，不再处理
      return
    }

    await this.handlers.handleUnknownMessage(message)
  }

  private static isWelcomeMessage(msg: string) {
    if (msg.includes('我通过了你的联系人验证请求，现在我们可以开始聊天了') || msg.includes('我已经添加了你，现在我们可以开始聊天了。') || msg.includes('请求添加你为朋友') || msg.includes('我通过了你的朋友验证请求，现在我们可以开始聊天了'))
      return true

    const welcomeMsgRegex = /^我是.{1,10}$/
    return welcomeMsgRegex.test(msg)
  }

  private async isFirstMessage(chatId: string) {
    return await this.chatHistoryServiceClient.getUserMessageCount(chatId) === 0
  }

  private async handleImageMessage(chatId: string, message: ScrmMessageToAiStaff, requireMultiModal?: boolean, imageAndText?: ImageAndText[]): Promise<string> {
    const url = message.filePath ?? ''

    // if (requireMultiModal && imageAndText) { // 交给后续图文 handler 统一处理，这里只做消息 push
    //   imageAndText.push({
    //     imageUrl: originalImageUrl
    //   })
    //
    //   return ''
    // }

    return this.handlers.handleImageMessage(url, chatId)
  }

  private async handleVideoMessage(chatId: string, message: ScrmMessageToAiStaff) {
    // message中的payload有视频url
    const videoUrl = message.filePath ?? ''
    return this.handlers.handleVideoMessage(videoUrl, chatId)
  }

}