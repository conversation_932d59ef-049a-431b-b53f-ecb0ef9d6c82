

export enum MaterialType {
    Text = 1,
    Article = 2,
    File = 3,
    Link = 4,
    Poster = 5,
    Image = 6,
    Audio = 7,
    Video = 8,
    Voice = 9,
    Channels = 10,
}

export interface IMaterial {
    source_id:string
    type:MaterialType
    title:string
    description:string
    es_id:string
    enable:boolean
    main_category:string
    sub_category:string
    data:Record<string, any> | null
}