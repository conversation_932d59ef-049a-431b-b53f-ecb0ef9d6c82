import { MessageSender } from '.'
import { MessageText, MessageImage, MessageVideo, MessageAudio, MessageFile, MessageSticker, MessageWecomVoice, MessageWecomCard, MessageWecomVideoChannel, MessageSendOption, MessageYCloudTemplate, MessageMaterial } from './type'
import logger from 'model/logger/logger'
import { HaoguMessageSender } from '../../message_handler/haogu/message_sender'
import { HaoguFileMessage, HaoguImageMessage, HaoguLinkMessage, HaoguMediaMessage, HaoguMessageType, HaoguVideoChannelMessage, HaoguVideoMessage } from '../../message_handler/haogu/type'
import { JsonValue } from 'model/prisma_client/runtime/library'
import { Config } from 'config'
import { IMaterial } from '../../rag/material/material_manager'
import { ChatHistoryService } from '../../chat_history/chat_history'

export class HaoguCommonMessageSender extends MessageSender {
  private haoguMessageSender:HaoguMessageSender
  private haoguChatIdTransfer:HaoguChatIdTransfer
  private haoguMaterial:HaoguMaterial

  constructor(haoguMessageSender:HaoguMessageSender, chatHistoryService:ChatHistoryService, haoguChatIdTransfer:HaoguChatIdTransfer, haoguMaterial:HaoguMaterial) {
    super(chatHistoryService)
    this.haoguMessageSender = haoguMessageSender
    this.haoguChatIdTransfer = haoguChatIdTransfer
    this.haoguMaterial = haoguMaterial
  }

  async sendText(chatId: string, msg: Omit<MessageText, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.text.trim())
    if (!opt?.force && isRepeated) {
      return
    }

    if (Config.setting.localTest) {
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId: '',
        conversationId: '',
        ai_msg:msg.text,
        type:HaoguMessageType.text
      }, {
        shortDes:(msg.description ? `[${msg.description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else {
      const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
      if (!chatInfo) {
        return
      }

      const {
        conversationId,
        toolUserId
      } = chatInfo
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId:toolUserId,
        conversationId:conversationId,
        ai_msg:msg.text,
        type:HaoguMessageType.text
      }, {
        shortDes:(msg.description ? `[${msg.description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    }
  }
  async sendWecomVoice(chatId: string, msg: Omit<MessageWecomVoice, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomCard(chatId: string, msg: Omit<MessageWecomCard, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomVideoChannel(chatId: string, msg: Omit<MessageWecomVideoChannel, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }

  async sendImage(chatId: string, msg: Omit<MessageImage, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendVideo(chatId: string, msg: Omit<MessageVideo, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendAudio(chatId: string, msg: Omit<MessageAudio, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendFile(chatId: string, msg: Omit<MessageFile, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendSticker(chatId: string, msg: Omit<MessageSticker, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendYCloudTemplate(chatId: string, msg: Omit<MessageYCloudTemplate, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }

  // async sendHaoguImage(chatId: string, msg: Omit<MessageHaoguImage, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.image,
  //     send_msg:<HaoguImageMessage>{
  //       fileUrl:msg.fileUrl,
  //       fileSize:msg.fileSize,
  //       imageWidth:msg.imageWidth,
  //       imageHeight:msg.imageHeight,
  //       md5:msg.md5
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguEmotion(chatId: string, msg: Omit<MessageHaoguEmotion, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.emotion,
  //     send_msg:<HaoguEmotionMessage>{
  //       fileUrl:msg.fileUrl,
  //       type:msg.emotionType
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguVideo(chatId: string, msg: Omit<MessageHaoguVideo, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.video,
  //     send_msg:<HaoguVideoMessage>{
  //       fileUrl:msg.fileUrl,
  //       fileSize:msg.fileSize,
  //       imageWidth:msg.imageWidth,
  //       imageHeight:msg.imageHeight,
  //       md5:msg.md5
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguFile(chatId: string, msg: Omit<MessageHaoguFile, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.file,
  //     send_msg:<HaoguFileMessage>{
  //       fileUrl:msg.fileUrl,
  //       fileSize:msg.fileSize,
  //       md5:msg.md5
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguLink(chatId: string, msg: Omit<MessageHaoguLink, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.link,
  //     send_msg:<HaoguLinkMessage>{
  //       title:msg.title,
  //       desc:msg.desc,
  //       url:msg.url,
  //       imageUrl:msg.imageUrl
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguVideoChannel(chatId: string, msg: Omit<MessageHaoguVideoChannel, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.videoChannel,
  //     send_msg:<HaoguVideoChannelMessage>{
  //       mpvVideoId:msg.mpvVideoId
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  // async sendHaoguMedia(chatId: string, msg: Omit<MessageHaoguMedia, 'type'>, opt?: MessageSendOption): Promise<void> {
  //   const isRepeated = await this.hasRepeatedMsg.hasRepeatedMsg(chatId, msg.description.trim())
  //   if (!opt?.force && isRepeated) {
  //     return
  //   }
  //   const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
  //   if (!chatInfo) {
  //     return
  //   }
  //   const {
  //     conversationId,
  //     toolUserId
  //   } = chatInfo
  //   await this.haoguMessageSender.sendById({
  //     chat_id:chatId,
  //     toolUserId:toolUserId,
  //     conversationId:conversationId,
  //     ai_msg:msg.description,
  //     type:HaoguMessageType.media,
  //     send_msg:<HaoguMediaMessage>{
  //       fileUrl:msg.fileUrl,
  //       fileSourceUrl:msg.fileSourceUrl,
  //       fileTime:msg.fileTime,
  //       md5:msg.md5
  //     }
  //   }, {
  //     shortDes:(msg.description ? `[${msg.description}]` : undefined),
  //     sop_id:opt?.sopId,
  //     round_id:opt?.roundId
  //   })
  // }
  async sendMaterial(chatId: string, msg: Omit<MessageMaterial, 'type'>, opt?: MessageSendOption): Promise<void> {
    const material = await this.haoguMaterial.searchMaterialById(msg.sourceId)
    if (!material) {
      logger.error(`没有找到这个素材，sourceId:${msg.sourceId}`)
      return
    }
    const description = msg.description ?? material.title
    const isRepeated = await this.chatHistoryService.isRepeatedMsg(chatId, description.trim())
    if (!opt?.force && isRepeated) {
      logger.warn({ chat_id:chatId }, '素材重复发送')
      return
    }
    const chatInfo = await this.haoguChatIdTransfer.getConversationIdAndToolUserId(chatId)
    if (!chatInfo) {
      return
    }
    const {
      conversationId,
      toolUserId
    } = chatInfo
    const data = material.data as any
    if (material.type == 1) {
      await this.haoguMessageSender.sendById({
        type: HaoguMessageType.text,
        chat_id: chatId,
        toolUserId: toolUserId,
        conversationId: conversationId,
        ai_msg: data.content
      }, {
        shortDes:(description ? `[${description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 2) {
      await this.haoguMessageSender.sendById({
        type:HaoguMessageType.link,
        chat_id: chatId,
        toolUserId: toolUserId,
        conversationId: conversationId,
        ai_msg:description,
        send_msg:<HaoguLinkMessage> {
          title:data.title,
          imageUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/haogu_logo.jpg',
          url:data.link_url,
          desc:material.description
        }
      }, {
        shortDes:(description ? `[${description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 3) {
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId:toolUserId,
        conversationId:conversationId,
        ai_msg:description,
        type:HaoguMessageType.file,
        send_msg:<HaoguFileMessage>{
          fileUrl:data.link_url,
          fileSize:data.file_size,
          md5:data.md5,
          filename:data.title
        }
      }, {
        shortDes:(description ? `[${description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 4) {
      await this.haoguMessageSender.sendById({
        type:HaoguMessageType.link,
        chat_id: chatId,
        toolUserId: toolUserId,
        conversationId: conversationId,
        ai_msg:description,
        send_msg:<HaoguLinkMessage> {
          title:data.title,
          imageUrl:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/haogu_logo.jpg',
          url:data.link_url,
          desc:material.description
        }
      }, {
        shortDes:(description ? `[${description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })

    } else if (material.type == 5) {
      // todo
    } else if (material.type == 6) {
      await this.haoguMessageSender.sendById({
        type: HaoguMessageType.image,
        chat_id: chatId,
        toolUserId: toolUserId,
        conversationId: conversationId,
        ai_msg: description,
        send_msg:<HaoguImageMessage>{
          fileUrl:data.link_url,
          fileSize:data.file_size,
          imageWidth:data.image_width,
          imageHeight:data.image_height,
          md5:data.md5
        }
      }, {
        shortDes:(description ? `[${description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 7) {
      // todo
    } else if (material.type == 8) {
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId:toolUserId,
        conversationId:conversationId,
        ai_msg:description,
        type:HaoguMessageType.media,
        send_msg:<HaoguVideoMessage>{
          fileSize:data.file_size,
          fileUrl:data.link_url,
          imageHeight:data.image_height,
          imageWidth:data.image_width,
          md5:data.md5
        }
      }, {
        shortDes:(msg.description ? `[${msg.description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 9) {
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId:toolUserId,
        conversationId:conversationId,
        ai_msg:description,
        type:HaoguMessageType.media,
        send_msg:<HaoguMediaMessage>{
          mediaId:data.id
        }
      }, {
        shortDes:(msg.description ? `[${msg.description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    } else if (material.type == 10) {
      await this.haoguMessageSender.sendById({
        chat_id:chatId,
        toolUserId:toolUserId,
        conversationId:conversationId,
        ai_msg:description,
        type:HaoguMessageType.videoChannel,
        send_msg:<HaoguVideoChannelMessage>{
          mpvVideoId:data.id
        }
      }, {
        shortDes:(msg.description ? `[${msg.description}]` : undefined),
        sop_id:opt?.sopId,
        round_id:opt?.roundId
      })
    }
  }
}

export abstract class HaoguChatIdTransfer {
  abstract getConversationIdAndToolUserId(chatId:string):Promise<{
    toolUserId:string
    conversationId:string
  } | null>
}

export abstract class HaoguMaterial {
  abstract searchMaterialById(sourceId: string): Promise< IMaterial| null>
}