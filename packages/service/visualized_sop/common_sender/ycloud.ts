import { MessageSender } from '.'
import { MessageText, MessageImage, MessageVideo, MessageAudio, MessageFile, MessageSticker, MessageWecomVoice, MessageWecomCard, MessageWecomVideoChannel, MessageSendOption, MessageYCloudTemplate, MessageMaterial } from './type'
import { WhatsappMessageMedia, WhatsappMessageTemplate, YCloudMessageSender } from '../../message_handler/ycloud/message_sender'
import logger from 'model/logger/logger'
import { WhatsappMessageTemplateComponent } from '@ycloud-cpaas/ycloud-sdk-node'
import { ChatHistoryService } from '../../chat_history/chat_history'

export class YCloudCommonMessageSender extends MessageSender {
  private ycloudMessageSender:YCloudMessageSender

  constructor(ycloudMessageSender:YCloudMessageSender, chatHistoryService:ChatHistoryService) {
    super(chatHistoryService)
    this.ycloudMessageSender = ycloudMessageSender
  }

  async sendText(chatId: string, msg: Omit<MessageText, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.text.trim())
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'text',
      ai_msg:msg.text
    }, {
      shortDes:(msg.description ? `[${msg.description}]` : undefined),
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendImage(chatId: string, msg: Omit<MessageImage, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'image',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        caption:'image'
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendVideo(chatId: string, msg: Omit<MessageVideo, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'video',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        caption:'video'
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendAudio(chatId: string, msg: Omit<MessageAudio, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'audio',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendFile(chatId: string, msg: Omit<MessageFile, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'document',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url,
        filename:msg.filename
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendSticker(chatId: string, msg: Omit<MessageSticker, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'sticker',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageMedia> {
        link:msg.url
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }
  async sendWecomVoice(chatId: string, msg: Omit<MessageWecomVoice, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomCard(chatId: string, msg: Omit<MessageWecomCard, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }
  async sendWecomVideoChannel(chatId: string, msg: Omit<MessageWecomVideoChannel, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }

  async sendYCloudTemplate(chatId: string, msg: Omit<MessageYCloudTemplate, 'type'>, opt?: MessageSendOption): Promise<void> {
    const isRepeated = await this.chatHistoryService.hasRepeatedMsg(chatId, msg.description)
    if (!opt?.force && isRepeated) {
      return
    }
    const components:WhatsappMessageTemplateComponent[] = [{
      type:'body',
      parameters:msg.variable.map((item) => ({
        type:'text',
        text:item
      }))
    }]
    if (msg.header?.type == 'image') {
      components.push({
        type:'header',
        parameters:[{
          type:'image',
          image:{
            link: msg.header.url
          }
        }]
      })
    } else if (msg.header?.type == 'document') {
      // const url = msg.header.url
      // const splitedUrl = url.split('/')
      // const name = splitedUrl[splitedUrl.length - 1]
      components.push({
        type:'header',
        parameters:[{
          type:'document',
          document:{
            link: msg.header.url,
          }
        }]
      })
    } else if (msg.header?.type == 'video') {
      components.push({
        type:'header',
        parameters:[{
          type:'video',
          video:{
            link:msg.header.url
          }
        }]
      })
    }
    await this.ycloudMessageSender.sendById({
      chat_id:chatId,
      type:'template',
      ai_msg:msg.description,
      send_msg:<WhatsappMessageTemplate>{
        name:msg.templateName,
        language:{
          code:msg.language
        },
        components:components
      }
    }, {
      shortDes:`[${msg.description}]`,
      sop_id:opt?.sopId,
      round_id:opt?.roundId
    })
  }

  async sendMaterial(chatId: string, msg: Omit<MessageMaterial, 'type'>, opt?: MessageSendOption): Promise<void> {
    logger.error('Method not implemented.')
  }

}