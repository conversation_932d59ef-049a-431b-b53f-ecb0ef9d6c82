import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { Config, YuHeAccountType } from 'config'
import { IGetTokenData, IGetTokenRequest, IXingyanResponse, IXingyanAccountConfig, YuHeAccountType as LocalYuHeAccountType } from './type'
import { Retry } from 'lib/retry/retry'
import { RedisCacheDB } from '../redis/redis_cache'

/**
 * 星炎云 API 客户端
 */
export class XingyanClient {
  private baseUrl: string
  private httpClient: AxiosInstance
  private tokenCacheKey: string
  private accountConfig: IXingyanAccountConfig

  constructor(accountConfig: IXingyanAccountConfig) {
    // 如果没有提供配置，则使用默认的全局配置方式
    this.accountConfig = accountConfig

    this.baseUrl = Config.setting.xingyan.baseUrl
    this.tokenCacheKey = this.accountConfig.accountType === LocalYuHeAccountType.ZhongShenTong
      ? 'zhongshentong_xingyan_access_token'
      : 'zhicheng_xingyan_access_token'

    this.httpClient = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  /**
   * 获取默认账户配置（向后兼容）
   */
  public static getDefaultAccountConfig(botId:string): IXingyanAccountConfig {
    const yuHeAccountType = Config.getYuHeAccountType(botId)
    const xingyanConfig = yuHeAccountType === YuHeAccountType.ZhiCheng
      ? Config.setting.xingyan.zhicheng
      : Config.setting.xingyan.zhongshentong

    return {
      accountType: yuHeAccountType === YuHeAccountType.ZhiCheng
        ? LocalYuHeAccountType.ZhiCheng
        : LocalYuHeAccountType.ZhongShenTong,
      cropId: xingyanConfig.cropId,
      secretKey: xingyanConfig.secretKey
    }
  }

  /**
   * 获取访问令牌
   * 先从缓存中获取，如果缓存中没有或已过期，则重新获取
   */
  public async getAccessToken(): Promise<string> {
    try {
      // 尝试从 Redis 缓存获取 token
      const cachedToken = await new RedisCacheDB(this.tokenCacheKey).get()
      if (cachedToken) {
        return cachedToken
      }
    } catch (error) {
      console.warn('从缓存获取 token 失败，将重新获取', error)
    }

    // 缓存中没有或获取失败，重新请求 token
    return await this.refreshAccessToken()
  }


  /**
   * 获取星炎云配置（静态方法，保持向后兼容）
   * @deprecated 建议使用依赖注入的方式传入配置
   */
  public static getXingYanConfig (botId:string) {
    if (Config.getYuHeAccountType(botId) === YuHeAccountType.ZhiCheng) {
      return Config.setting.xingyan.zhicheng
    } else {
      return Config.setting.xingyan.zhongshentong
    }
  }

  /**
   * 刷新访问令牌
   */
  private async refreshAccessToken(): Promise<string> {
    const params: IGetTokenRequest = {
      cropId: this.accountConfig.cropId,
      secretKey: this.accountConfig.secretKey
    }

    try {
      const response = await this.httpClient.post<IXingyanResponse<IGetTokenData>>('/getAccessToken', params)

      if (response.data.code !== 200) {
        throw new Error(`获取 token 失败: ${response.data.msg}`)
      }

      const tokenData = response.data.data
      const token = tokenData.token

      // 计算过期时间，提前5分钟过期以确保安全
      const expiresAt = new Date(tokenData.expiresIn - 5 * 60 * 1000)
      const ttl = Math.floor((expiresAt.getTime() - Date.now()) / 1000)

      // 缓存 token
      if (ttl > 0) {
        await new RedisCacheDB(this.tokenCacheKey).set(token, ttl)
      }

      return token
    } catch (error) {
      console.error('刷新 token 失败:', error)
      throw new Error(`获取星炎云 token 失败: ${error}`)
    }
  }

  /**
   * 发送 GET 请求
   * @param endpoint API 端点
   * @param params 请求参数
   */
  public async get<T>(endpoint: string, params: any = {}): Promise<AxiosResponse<IXingyanResponse<T>>> {
    const token = await this.getAccessToken()

    try {
      return await Retry.retry(3, async () => {
        try {
          const response =  await this.httpClient.get<IXingyanResponse<T>>(endpoint, {
            params,
            headers: {
              'Token': token
            }
          })

          XingyanClient.logError(endpoint, params, response.data)

          return response
        } catch (e) {
          if (axios.isAxiosError(e) && e.response && e.response.status === 401) {
            // 重新获取 token
            const newToken = await this.refreshAccessToken()
            // 重试请求
            return await this.httpClient.get<IXingyanResponse<T>>(endpoint, {
              params,
              headers: {
                'Token': newToken
              }
            })
          }

          throw e
        }
      })
    } catch (error: any) {
      if (error.response) {
        console.error(JSON.stringify(error.response.data, null, 2))
      }
      throw new Error(`请求失败：${endpoint}\n${error}`)
    }
  }

  /**
   * 发送 POST 请求
   * @param endpoint API 端点
   * @param data 请求数据
   */
  public async post<T>(endpoint: string, data: any): Promise<AxiosResponse<IXingyanResponse<T>>> {
    const token = await this.getAccessToken()

    try {
      return await Retry.retry(3, async () => {
        try {
          const response =  await this.httpClient.post<IXingyanResponse<T>>(endpoint, data, {
            headers: {
              'Token': token
            }
          })

          XingyanClient.logError(endpoint, data, response.data)

          return response
        } catch (e) {
          if (axios.isAxiosError(e) && e.response && e.response.status === 401) {
            // 重新获取 token
            const newToken = await this.refreshAccessToken()
            // 重试请求
            return await this.httpClient.post<IXingyanResponse<T>>(endpoint, data, {
              headers: {
                'Token': newToken
              }
            })
          }

          throw e
        }
      }, {
        delayFunc: (count) => count * 200
      })
    } catch (error: any) {
      if (error.response) {
        console.error(error.response.status, JSON.stringify(error.response.data, null, 2))
      }
      throw new Error(`请求失败：${endpoint}\n ${JSON.stringify(data)}: ${error}`)
    }
  }


  private static logError<T>(endpoint: string, payload: any,  response: IXingyanResponse<T>) {
    if (!response.code && response.code !== 200) {
      console.error(`请求失败：${endpoint}\n ${JSON.stringify(payload)}: ${JSON.stringify(response)}`)
    }
  }
}
