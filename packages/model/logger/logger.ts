import pino, { multistream } from 'pino'
import pretty from 'pino-pretty'
import { MongoDBStream } from './mongoStream'
import SentryLogger from './sentryLogger'
import { ErrorContext } from './errorTypes'
import SentryInitializer from './sentryInit'

interface LoggerOptions {
    [key: string]: any
}

/**
 * 日志输出尽量使用这个类，避免直接使用 console.log
 */
class logger {
  private static instance?: pino.Logger<string>

  private constructor(options: LoggerOptions) {
  }

  public static getInstance(): pino.Logger<string> {
    if (!this.instance) {
      // 自动初始化Sentry
      this.initializeSentry()

      const streams = [
        {
          level: 'trace',
          stream: pretty({
            colorize: true, // 如果你想要颜色化
            translateTime: 'SYS:mm-dd HH:MM:ss',
            minimumLevel: 'trace'
          })
        },
        {
          level: 'trace',
          stream: new MongoDBStream(),
        },
      ]

      this.instance = pino({ level: 'trace' }, multistream(streams))
    }

    return this.instance
  }

  /**
   * 自动初始化Sentry
   * 只在第一次使用logger时调用
   */
  private static initializeSentry() {
    try {
      SentryInitializer.init()
    } catch (error) {
      console.warn('Sentry自动初始化失败:', error)
    }
  }



  private static _log(level: string, ...args: any[]) {
    const instance = this.getInstance()
    const fn = instance[level].bind(instance)

    if (typeof args [0] === 'string') {
      fn(args.map((arg) => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg)
        }

        return String(arg)
      }).join(' '))
    } else {
      const obj = args [0]
      const msg = args.slice(1).map((arg) => {
        if (typeof arg === 'object') {
          return JSON.stringify(arg)
        }

        return String(arg)
      }).join(' ')
      fn (obj, msg)
    }
  }
  public static trace (obj: unknown, msg?: string, ...args: any []): void
  public static trace (msg: string, ...args: any []): void
  public static trace(...args: any[]) {
    this._log('trace', ...args)
  }

  public static debug (obj: unknown, msg?: string, ...args: any []): void
  public static debug (msg: string, ...args: any []): void
  public static debug(...args: any[]) {
    this._log('debug', ...args)
  }

  // 注意：这些是函数重载的签名
  public static log (obj: unknown, msg?: string, ...args: any []): void
  public static log (msg: string, ...args: any []): void
  public static log(...args: any[]) {
    this._log('info', ...args)
  }

  public static warn (obj: unknown, msg?: string, ...args: any []): void
  public static warn (msg: string, ...args: any []): void
  public static warn(...args: any[]) {
    this._log('warn', ...args)

    // 可选：自动上报警告到Sentry（默认关闭）
    SentryLogger._autoReport('warn', args)
  }


  // 类型化的错误上报重载（推荐使用）
  public static error(context: ErrorContext, msg: string, error?: Error): void
  // 兼容旧格式的重载
  public static error (obj: unknown, msg?: string, ...args: any []): void
  public static error (msg: string, ...args: any []): void
  public static error(...args: any[]) {
    let logTrace = false

    for (const arg of args) {
      if (arg instanceof Error) {
        if (arg.stack) {
          this._log('error', arg.stack)
          logTrace = true
        }
      }
    }

    if (!logTrace) {
      console.trace()
    }

    this._log('error', args)

    // 自动上报错误到Sentry
    SentryLogger._autoReport('error', args)
  }


  // 暴露Sentry上报功能
  public static sentry = SentryLogger
}

export default logger