// 错误上报的统一类型定义
// 所有错误上报必须使用预定义的枚举值，避免随意填写
export enum ErrorComponent {
  //目前初步划分，可以再调整，这里定义的组件类型命名应当通过，注意不要带上任何独属于某个项目的名称，抽象成更通用概括性的模块
  // === 业务逻辑层 ===

  // 消息收发服务 - 消息分发
  MESSAGE_ROUTING = 'MESSAGE_ROUTING',

  // 对话处理服务 - 工作流引擎、对话逻辑
  CONVERSATION_FLOW = 'CONVERSATION_FLOW',        // workflow.ts (工作流引擎、节点处理)

  // 业务事件处理 - CRM/DDM事件、客户管理
  BUSINESS_EVENT = 'BUSINESS_EVENT',

  // === AI智能决策层 ===

  // 智能规划服务 - 任务规划、时间调度
  AI_PLANNING = 'AI_PLANNING',                    // big_planner.ts, planner.ts

  // 智能上下文服务 - 知识检索、记忆管理、阶段判断
  AI_CONTEXT = 'AI_CONTEXT',                      // context.ts, rag/, stage_manager.ts

  // SOP智能处理 - 标准流程执行
  AI_SOP = 'AI_SOP',                              // visualized_sop/

  // === 服务层 ===

  // 数据服务 - 外部API调用、课程数据、客户数据
  DATA_SERVICE = 'DATA_SERVICE',                  // get_data.ts, api_instance.ts

  // 数据存储 - 数据库操作、状态管理
  DATA_STORAGE = 'DATA_STORAGE',                  // database/, base_instance.ts

  // 人工服务 - 转人工处理
  HUMAN_SERVICE = 'HUMAN_SERVICE',                // human_transfer.ts

  // === 基础设施层 ===

  // 基础设施 - 依赖注入、监控、配置
  INFRASTRUCTURE = 'INFRASTRUCTURE',              // instance/, prometheus/

  // 在所有调用层面，单独区分，监控所有外部api
  EXTERNAL_API_CALL = 'EXTERNAL_API_CALL',          // 外部API调用
}


// 错误上报的上下文接口
export interface ErrorContext {
  // === 必填字段 ===
  chat_id: string                     // 用户聊天ID（核心追踪标识）
  component: ErrorComponent           // 组件类型（预定义枚举）

  // === 可选字段 ===
  // 用户相关
  user_id?: string                    // 用户ID

  // 请求相关
  request_id?: string                 // 请求ID
  session_id?: string                 // 会话ID

  // 业务相关
  course_no?: string                  // 课程编号
  day?: number                        // 天数
  task_id?: string                    // 任务ID
  sop_name?: string                   // SOP名称
  round_id?: string                   // 轮次ID
  plan_count?: number                 // 计划数量
  course_type?: string                // 课程类型
  msg_type?: string                   // 消息类型

  // HTTP相关
  method?: string                     // HTTP方法
  url?: string                        // 请求URL
  status_code?: number                // 响应状态码
  user_agent?: string                 // 用户代理
  ip?: string                         // IP地址

  // 技术相关
  duration?: number                   // 操作耗时(ms)
  retry_count?: number                // 重试次数
  batch_size?: number                 // 批处理大小

  // 错误相关
  error_code?: string                 // 错误码
  upstream_service?: string           // 上游服务名

  // 其他扩展字段（严格控制，不鼓励随意添加）
  [key: string]: any
}

// 便捷的错误日志类型
export interface ErrorLogEntry {
  context: ErrorContext
  message: string
  error?: Error
}