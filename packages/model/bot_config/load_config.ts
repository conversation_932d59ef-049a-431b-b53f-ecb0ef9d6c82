import { IWechatConfig } from 'config/interface'
import { PrismaMongoClient } from '../mongodb/prisma'
import { Config } from 'config'
import { RedisDB } from '../redis/redis'
import logger from '../logger/logger'

interface IMoerEnterpriseConfig {
  notifyGroupId: string
  classGroupId: string
  isGroupOwner?: boolean
  proxyGroupNotify?: boolean
}


export async function loadConfigByWxId(id: string, projectName:string|undefined = Config.setting.projectName): Promise<IWechatConfig> {
  if (!projectName) {
    logger.error('没有projectName')
    process.exit(1)
  }
  const redisClient = RedisDB.getInstance()
  const cacheKey = getBotConfigCacheKey(projectName, id)
  const cacheResult = await redisClient.get(cacheKey)
  if (cacheResult) {
    return JSON.parse(cacheResult) as IWechatConfig
  } else {
    const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
      {
        where: {
          enterpriseName: projectName,
          wechatId: id
        }
      }
    )
    if (!config) {
      throw new Error('Config not found')
    }
    const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

    const account =  {
      orgToken: config.orgToken,
      nickname: config.accountName,
      wechatId: config.wechatId,
      botUserId: config.botUserId,
      address: config.address,
      port: Number(config.port),
      notifyGroupId: enterpriseConfig.notifyGroupId,
      classGroupId: enterpriseConfig.classGroupId,
      isGroupOwner: enterpriseConfig.isGroupOwner,
      proxyGroupNotify: enterpriseConfig.proxyGroupNotify
    }

    const result = {
      orgToken: account.orgToken,
      id: account.wechatId,
      name: account.nickname,
      botUserId: account.botUserId,
      notifyGroupId: account.notifyGroupId,
      classGroupId: account.classGroupId,
      isGroupOwner: account.isGroupOwner,
      proxyGroupNotify: account.proxyGroupNotify,
      createdTime: config.createdAt
    }
    await redisClient.set(cacheKey, JSON.stringify(result))
    return result
  }
}

function getBotConfigCacheKey(enterPriseName:string, botId:string): string {
  return `${enterPriseName}:bot_config:${botId}`
}