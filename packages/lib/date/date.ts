import dayjs, { ManipulateType } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

export class DateHelper {
  public static getFormattedDate (date: Date, includeTime: boolean = true): string {
    const year = date.getFullYear ()
    const month = date.getMonth () + 1
    const day = date.getDate ()
    const hour = date.getHours ()
    const minute = date.getMinutes ()
    const second = date.getSeconds ()
    const dayOfWeek = date.getDay ()

    // 如果是一位数字，则在前面补 0
    const monthStr = month < 10 ? `0${month}` : month
    const dayStr = day < 10 ? `0${day}` : day
    const hourStr = hour < 10 ? `0${hour}` : hour
    const minuteStr = minute < 10 ? `0${minute}` : minute
    const secondStr = second < 10 ? `0${second}` : second

    // 定义一个表示周几的字符串数组
    const daysOfWeek = ['日', '一', '二', '三', '四', '五', '六']
    const dayOfWeekStr = `周${daysOfWeek[dayOfWeek]}`

    let timeStr = `${year}-${monthStr}-${dayStr}${dayOfWeekStr}`
    if (includeTime)
      timeStr += `${hourStr}:${minuteStr}:${secondStr}`

    return timeStr
  }

  public static convertToUTC8(date: Date): Date {
    // 获取当前时区偏移（分钟）
    const currentTimezoneOffset = date.getTimezoneOffset()

    // 计算东8区（UTC+8）的时区偏移（分钟）
    const utc8Offset = -8 * 60 // -8 小时，因为 getTimezoneOffset() 返回负值代表正时区

    // 判断是否已经是东8区时间
    if (currentTimezoneOffset === utc8Offset) {
      return date // 如果已经是东8区时间，直接返回原日期
    }

    // 将当前日期时间转换为 UTC 时间
    const utcTime = date.getTime() + currentTimezoneOffset * 60 * 1000

    // 将 UTC 时间加上东8区偏移量，得到东8区时间
    return new Date(utcTime + utc8Offset * 60 * 1000)
  }

  public static isSame(date1: Date, date2: Date, unit: 'day' | 'hour' |'minute' |'second' |'millisecond'): boolean {
    const date_1 = dayjs(date1)
    const date_2 = dayjs(date2)

    return date_1.isSame(date_2, unit)
  }

  public static isAfter(date1: Date, date2: Date): boolean {
    const date_1 = dayjs(date1)
    const date_2 = dayjs(date2)

    return date_1.isAfter(date_2)
  }

  // 只对小时进行比较，判断 '前面的时间' 是否在 '后面的时间' 之后
  public static isTimeAfter(time: string, compareTime: string): boolean {
    const [hours, minutes, seconds] = time.split(':').map(Number)
    const [compareHours, compareMinutes, compareSeconds] = compareTime.split(':').map(Number)

    if (hours !== compareHours) return hours > compareHours
    if (minutes !== compareMinutes) return minutes > compareMinutes
    return seconds > compareSeconds
  }


  // 只对小时进行比较，判断 '前面的时间' 是否在 '后面的时间' 之前
  public static isTimeBefore(time: string, compareTime: string): boolean {
    const [hours, minutes, seconds] = time.split(':').map(Number)
    const [compareHours, compareMinutes, compareSeconds] = compareTime.split(':').map(Number)

    if (hours !== compareHours) return hours < compareHours
    if (minutes !== compareMinutes) return minutes < compareMinutes
    return seconds < compareSeconds
  }

  public static isBefore(date1: Date, date2: Date): boolean {
    const date_1 = dayjs(date1)
    const date_2 = dayjs(date2)

    return date_1.isBefore(date_2)
  }

  /**
   * 返回以 unit 为单位， date2 - date1 的差值
   * @param date1
   * @param date2
   * @param unit
   */
  public static diff(date1: Date, date2: Date, unit:  'millisecond' | 'second' | 'minute' | 'hour' | 'day' | 'month' | 'year' | 'date'): number {
    const date_1 = dayjs(date1)
    const date_2 = dayjs(date2)

    return date_2.diff(date_1, unit)
  }

  public static add(date: Date, amount: number, unit: 'millisecond' | 'second' | 'minute' | 'hour' | 'day' | 'month' | 'year' | 'date'): Date {
    return dayjs(date).add(amount, unit as ManipulateType).toDate()
  }

  /**
   * Get the date of a specific time X days later
   * @param date - The base date
   * @param days - The number of days to add
   * @param targetHour - The target hour (24-hour format)
   * @param targetMinute - The target minute (default: 0)
   * @param targetSecond - The target second (default: 0)
   * @returns The calculated Date
   */
  public static addDaysWithTime(
    date: Date,
    days: number,
    targetHour: number,
    targetMinute: number = 0,
    targetSecond: number = 0
  ): Date {
    const newDate = dayjs(date).add(days, 'day')
    const resultDate = newDate
      .hour(targetHour)
      .minute(targetMinute)
      .second(targetSecond)
      .millisecond(0)
    return resultDate.toDate()
  }

  /**
   * 解析 时间字符串，默认为 utc-8 中国时区
   * @param dateStr
   * @param tz
   */
  public static parseDate(dateStr: string, tz = 'Asia/Shanghai'): Date {
    dayjs.extend(utc)
    dayjs.extend(timezone)

    // 将时间解析为 UTC+8 时区
    const dateInUtc8 = dayjs.tz(dateStr, tz)

    // 转换为 Date 对象
    return dateInUtc8.toDate()
  }

  public static formatDate(date: Date, format: string): string {
    return dayjs(date).format(format)
  }

  public static getTimeOfDay(date: Date) {
    const hours = date.getHours()
    if (hours >= 6 && hours < 12) {
      return '早上'
    } else if (hours >= 12 && hours < 14) {
      return '中午'
    } else if (hours >= 14 && hours < 18) {
      return '下午'
    } else if (hours >= 18 && hours < 23) {
      return '晚上'
    } else if (hours >= 23 && hours < 25) {
      return '深夜'
    } else {
      return '凌晨'
    }
  }

  static getWeekDay() {
    // 7 代表周日，1 代表周一，以此类推
    if (new Date().getDay() === 0) {
      return 7
    }

    return new Date().getDay()
  }

  public static msToTime(duration: number): string {
    const seconds = Math.floor((duration / 1000) % 60)
    const minutes = Math.floor((duration / (1000 * 60)) % 60)
    const hours = Math.floor((duration / (1000 * 60 * 60)) % 24)
    const days = Math.floor(duration / (1000 * 60 * 60 * 24))

    return `${days}天 ${hours}小时 ${minutes}分 ${seconds}秒`
  }


}