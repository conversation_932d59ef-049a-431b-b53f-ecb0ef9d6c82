'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'
import axios from 'axios'
import dayjs from 'dayjs'
import { DataService } from 'yuhe/helper/getter/get_data'
import { Config } from 'config'
import { chatStateStoreClient } from 'yuhe/service/base_instance'
import { IChattingFlag } from 'yuhe/state/user_flags'
import { YuheUserData } from '../type/user'

import { Node } from 'service/agent/workflow'

Config.setting.projectName = 'yuhe'

export async function queryChats(nameOrPhone:string, courseNo?:number):Promise<YuheUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:200,
    orderBy:{
      created_at:'desc'
    },
    where:{
      AND:[
        {
          course_no:courseNo,
        },
        {
          OR:[
            { phone:{ contains:nameOrPhone } },
            { id:nameOr<PERSON>hone },
            { contact:{
              is:{
                wx_name: {
                  contains: nameOr<PERSON><PERSON>
                }
              }
            }
            }
          ]
        }
      ]
    } })
  return queryNameResult as unknown as YuheUserData[]
}

export async function queryDefaultChats() {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const queryNameResult = await mongoClient.chat.findMany({
    take:10,
    orderBy: {
      created_at: 'desc' // 按照 create_at 降序排列
    },
  })
  return queryNameResult as unknown as YuheUserData[]
}

export async function queryChatById(id:string):Promise<YuheUserData | null> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const result = await mongoClient.chat.findFirst({ where:{
    id
  } })
  return result as unknown as (YuheUserData | null)
}

export async function queryChatsWithoutAi(courseNo?:number): Promise<YuheUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const result = await mongoClient.chat.findMany({ where:{
    is_human_involved:true,
    course_no:courseNo
  },
  take: courseNo ? undefined : 50
  })
  return result as unknown as YuheUserData[]
}

export async function queryChatsWithoutPhone(courseNo?:number):Promise<YuheUserData[]> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const result = await mongoClient.chat.findRaw({ filter: { phone: { $exists: false }, course_no: courseNo }, options:{
    limit: courseNo ? undefined : 50
  } }) as unknown as any[]
  for (let i = 0; i < result.length; i++) {
    result[i].id = result[i]['_id']
  }
  return result
}

export async function changeIsHumanInvolved(chatId:string, isHumanInvolved:boolean) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_human_involved:isHumanInvolved } })
}

export async function changeIsStopGroupPush(chatId:string, isStopGroupPush:boolean) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data: { is_stop_group_push:isStopGroupPush } })
}

export async function changeCourseNo(chatId:string, courseNo:number) {
  await DataService.delayCourseNo(chatId, courseNo)
}

export async function changePhone(chatId:string, phone:string) {
  await DataService.bindPhone(chatId, phone)
}

export async function changeNextStage(chatId:string, stage:Node) {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/change_stage`, {
    method:'POST',
    data:{
      chatId: chatId,
      stage: stage
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function clearCache(chatId:string):Promise<void> {
  'use server'
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const chatInfo = await mongoClient.chat.findFirst({ where:{ id:chatId } })
  if (!chatInfo) {
    throw '没有找到这个人'
  }
  const mongoConfigInstance = AdminPrismaMongoClient.getConfigInstance()
  const botInfo = await mongoConfigInstance.config.findFirst({ select:{ address:true }, where:{ wechatId:chatInfo.wx_id } })
  if (!botInfo) {
    throw '没有找到对应的机器人配置'
  }
  const address = botInfo.address
  await axios(`${address}/test/event/clear_cache`, {
    method:'POST',
    data:{
      chatId: chatId,
    }
  }).then((res) => {
    if (res.data.code != 200) {
      throw res.data.msg
    }
  })
}

export async function getChatByCourseWeekRange(
  minCourseWeek: number,
  maxCourseWeek: number
) {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  const chatList = await mongoClient.chat.findMany({
    where: {
      course_no: {
        gte: minCourseWeek,
        lte: maxCourseWeek,
      },
    },
  })
  return chatList
}

export async function updateIsPaid(chatId:string, isPaid:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_complete_payment': isPaid } }
      }
    ]
  })
}

export async function updateIsInviteGroupAfterPayment(chatId:string, isInviteGroupFailAfterPayment:boolean): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.$runCommandRaw({
    update: 'chat', // 集合名，注意区分大小写
    updates: [
      {
        q: { _id: chatId }, // 查询条件
        u: { $set: { 'chat_state.state.is_invite_group_fail_after_payment': isInviteGroupFailAfterPayment } }
      }
    ]
  })
}

export async function updateIp(chatId:string, ip:string):Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ ip } })
}

export async function updatePayTime(chatId:string, time:string): Promise<void> {
  const mongoClient = AdminPrismaMongoClient.getYuheInstance()
  await mongoClient.chat.update({ where:{ id:chatId }, data:{ pay_time:dayjs(time).toDate() } })
}

export async function updatePaymentNumber(chatId:string, number:number):Promise<void> {
  await chatStateStoreClient.clearCache(chatId)
  await chatStateStoreClient.update(chatId, {
    state:<IChattingFlag>{
      payment_number:number
    }
  })
  await chatStateStoreClient.clearCache(chatId)
}