import { Counter } from 'lib/prometheus_client/index'

export const talkCounter = new Counter({
  name: 'yuhe_freetalk',
  help: 'The times of freetalk',
  labelNames:['bot_id']
})

export const receiveMessageCount = new Counter({
  name: 'yuhe_message_receive',
  help: 'The times of yuhe message receive',
  labelNames:['bot_id']
})

export const queryCourseInformationCounter = new Counter({
  name: 'yuhe_query_course_information',
  help: 'The times of yuhe query course information',
})

export const queryCourseInformationErrorCounter = new Counter({
  name: 'yuhe_query_course_information_error',
  help: 'The times of yuhe query course information error',
})