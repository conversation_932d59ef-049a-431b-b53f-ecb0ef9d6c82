import { IChattingFlag } from '../state/user_flags'
import { DataService } from '../helper/getter/get_data'
import { XingyanAPI } from 'model/xingyan/api'
import { JuziAPI } from 'model/juzi/api'
import { getBotId, getUserId } from 'config/chat_id'
import logger from 'model/logger/logger'
import dayjs from 'dayjs'
import pLimit from 'p-limit'
import { PrismaMongoClient } from '../database/prisma'
import { chatStateStoreClient } from '../service/base_instance'
import { getXingyanApiByBotId } from '../service/xingyan'

// 特征工程配置
const FEATURE_CONFIG = {
  // 可调权重
  conversationWeight: 0.2,
  lectureEngagementWeight: 0.65,
  hwWeight: 0.15,

  // 相对于发言积极性的完课权重
  lecWeight: 0.85,

  // 课程时长（分钟）
  lecture1Length: 173,
  lecture2Length: 232,
  lecture3Length: 208,
  lecture4Length: 191,

  // 作业基础分数
  hwBaseScore: 30,

  // 缺课阈值
  absenceThreshold: 0.2
}

// 意向度等级标准
export enum IntentLevel {
  HIGH = '高意向',
  MEDIUM_HIGH = '中高意向',
  MEDIUM = '中意向',
  LOW = '低意向',
  ZERO = '零意向'
}

// 客户意向度数据接口
interface CustomerIntentData {
  chatId: string
  userId: string
  courseNo: number
  userName: string

  // 对话轮次
  day0ConversationRounds: number
  day1ConversationRounds: number
  day2ConversationRounds: number
  day3ConversationRounds: number
  day4ConversationRounds: number

  // 课程参与度
  day1CourseDuration: number
  day2CourseDuration: number
  day3CourseDuration: number
  day4CourseDuration: number

  // 发言活跃度
  day1LiveComments: number
  day2LiveComments: number
  day3LiveComments: number
  day4LiveComments: number

  // 作业完成情况
  day1Homework: boolean
  day2Homework: boolean
  day3Homework: boolean
  day4Homework: boolean

  // 计算得出的分数
  day1IntentScore: number
  day2IntentScore: number
  day3IntentScore: number
  day4IntentScore: number

  // 当前最高意向度分数
  maxIntentScore: number
  currentIntentLevel: IntentLevel

  // 意向度变化分析
  intentChanges?: {
    day1ToDay2: number
    day2ToDay3: number
    day3ToDay4: number
    maxChange: number
    minChange: number
    averageChange: number
    trend: '意向度提升' | '意向度下降' | '意向度稳定' | '意向度波动'
    significantChanges: number
    levelChanges: number
  }
}

// 同期统计数据接口
interface CohortStatistics {
  courseNo: number
  q95Day2Before: number
  q95Day2: number
  q95Day3: number
  q95Day4: number
  day1CommentStats: { mean: number; std: number }
  day2CommentStats: { mean: number; std: number }
  day3CommentStats: { mean: number; std: number }
  day4CommentStats: { mean: number; std: number }
}

// 数学函数
function sigmoid(x: number): number {
  return 1 / (1 + Math.exp(-x))
}

function calculateZScore(value: number, mean: number, std: number): number {
  return std > 0 ? (value - mean) / std : 0
}

function scoreConversation(totalRounds: number, q_value: number): number {
  if (totalRounds <= 0) {
    return 0
  }

  const logRounds = Math.log(1 + totalRounds)
  const maxLogRounds = Math.log(1 + q_value)

  return Math.min(100, (logRounds / maxLogRounds) * 100)
}

export function getIntentLevel(score: number): IntentLevel {
  if (score >= 80) {
    return IntentLevel.HIGH
  } else if (score >= 65) {
    return IntentLevel.MEDIUM_HIGH
  } else if (score >= 40) {
    return IntentLevel.MEDIUM
  } else if (score >= 20) {
    return IntentLevel.LOW
  } else {
    return IntentLevel.ZERO
  }
}

/**
 * 计算同期统计数据 - 优化内存使用
 */
export async function calculateCohortStatistics(courseNo: number): Promise<CohortStatistics> {
  const mongoClient = PrismaMongoClient.getInstance()

  // 获取同期所有客户数量
  const customerCount = await mongoClient.chat.count({
    where: {
      course_no: courseNo,
      phone: {
        not: null
      }
    }
  })

  logger.log(`计算期数 ${courseNo} 的统计数据，共 ${customerCount} 个客户`)

  // 分批处理客户数据，避免一次性加载所有数据
  const batchSize = 100 // 每批处理100个客户
  const totalBatches = Math.ceil(customerCount / batchSize)

  let allDayRounds: number[][] = []
  let allDayComments: number[][] = []

  for (let batch = 0; batch < totalBatches; batch++) {
    const skip = batch * batchSize

    // 加载数据后监测
    const customers = await mongoClient.chat.findMany({
      where: {
        course_no: courseNo,
        phone: {
          not: null
        }
      },
      select: {
        id: true,
        phone: true
      },
      skip,
      take: batchSize
    })
    logMem(`batch ${batch + 1} loaded`)

    logger.log(`处理批次 ${batch + 1}/${totalBatches}，客户 ${skip + 1}-${skip + customers.length}`)

    // 限制并发数，避免内存溢出
    const limit = pLimit(30)

    const batchPromises = customers.map(async (customer) => {
      return limit(async () => {
        try {
          // 只获取必要的聊天历史数据
          const messages = await mongoClient.chat_history.findMany({
            where: {
              chat_id: customer.id,
              role: 'user'
            },
            select: {
              created_at: true
            },
            orderBy: {
              created_at: 'asc'
            },
            take: 5000 // 减少最大消息数量
          })

          // 计算每天的消息数量
          const dayRounds = [0, 0, 0, 0, 0]
          for (const message of messages) {
            try {
              const scheduleTime = await DataService.getCurrentTimeByDate(customer.id, message.created_at)
              if (scheduleTime.day >= 0 && scheduleTime.day <= 4) {
                dayRounds[scheduleTime.day]++
              }
            } catch (error) {
              continue
            }
          }

          // 获取课程信息（限制并发）
          const courseInfo = await DataService.getCourseInfoByChatId(customer.id)
          const dayComments = [0, 0, 0, 0]

          if (courseInfo) {
            for (const info of courseInfo) {
              const day = info.day
              const liveId = info.liveId

              try {
                const xingyanApi = getXingyanApiByBotId(getBotId(customer.id))
                const messages = await xingyanApi.getRoomMsgPage(`${liveId}`, customer.phone ?? undefined)
                const commentsCount = messages?.length || 0

                if (day >= 1 && day <= 4) {
                  dayComments[day - 1] = commentsCount
                }
              } catch (error) {
                // 忽略单个API调用失败
                continue
              }
            }
          }

          return { dayRounds, dayComments }
        } catch (error) {
          logger.warn(`处理客户 ${customer.id} 统计数据失败:`, error)
          return { dayRounds: [0, 0, 0, 0, 0], dayComments: [0, 0, 0, 0] }
        }
      })
    })

    const batchResults = await Promise.allSettled(batchPromises)
    logMem(`batch ${batch + 1} processed`)

    // 收集批次结果
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        allDayRounds.push(result.value.dayRounds)
        allDayComments.push(result.value.dayComments)
      }
    }

    // 批次间延迟，给GC时间
    if (batch < totalBatches - 1) {
      if (global.gc) {
        global.gc()
        logMem(`batch ${batch + 1} after GC`)
      }
      await new Promise((resolve) => setTimeout(resolve, 1000))
    }
  }

  // 计算统计数据
  const day2BeforeRounds = allDayRounds.map((r) => r[0] + r[1])
  const day2Rounds = allDayRounds.map((r) => r[2])
  const day3Rounds = allDayRounds.map((r) => r[3])
  const day4Rounds = allDayRounds.map((r) => r[4])

  const q95Day2Before = calculatePercentile(day2BeforeRounds, 95)
  const q95Day2 = calculatePercentile(day2Rounds, 95)
  const q95Day3 = calculatePercentile(day3Rounds, 95)
  const q95Day4 = calculatePercentile(day4Rounds, 95)

  // 计算评论统计
  const day1Comments = allDayComments.map((c) => c[0])
  const day2Comments = allDayComments.map((c) => c[1])
  const day3Comments = allDayComments.map((c) => c[2])
  const day4Comments = allDayComments.map((c) => c[3])

  const day1CommentStats = calculateStats(day1Comments)
  const day2CommentStats = calculateStats(day2Comments)
  const day3CommentStats = calculateStats(day3Comments)
  const day4CommentStats = calculateStats(day4Comments)

  // 清理内存
  allDayRounds = []
  allDayComments = []

  logMem('calculateCohortStatistics end')
  return {
    courseNo,
    q95Day2Before,
    q95Day2,
    q95Day3,
    q95Day4,
    day1CommentStats,
    day2CommentStats,
    day3CommentStats,
    day4CommentStats
  }
}

// 辅助函数
function calculatePercentile(values: number[], percentile: number): number {
  const sorted = values.slice().sort((a, b) => a - b)
  const index = Math.ceil((percentile / 100) * sorted.length) - 1
  return sorted[Math.max(0, index)] || 0
}

function calculateStats(values: number[]): { mean: number; std: number } {
  if (values.length === 0) return { mean: 0, std: 0 }

  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  const std = Math.sqrt(variance)

  return { mean, std }
}

/**
 * 计算单个客户的意向度分数（基于同期统计数据）
 */
export async function calculateCustomerIntentScore(chatId: string, cohortStats: CohortStatistics): Promise<CustomerIntentData | null> {
  try {
    const mongoClient = PrismaMongoClient.getInstance()
    const chat = await mongoClient.chat.findUnique({
      where: { id: chatId }
    })

    if (!chat || !chat.phone) {
      logger.warn(`客户数据不存在或缺少手机号: ${chatId}`)
      return null
    }

    const userId = getUserId(chatId)
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
    const courseInfo = await DataService.getCourseInfoByChatId(chatId)
    const xingyanApi = getXingyanApiByBotId(getBotId(chatId))

    // 使用更安全的方式获取聊天历史，避免BSON对象过大错误
    let messages: any[] = []
    try {
      // 直接使用限制数量的查询，避免BSON对象过大错误
      messages = await mongoClient.chat_history.findMany({
        where: {
          chat_id: chatId,
          role: 'user'
        },
        select: {
          created_at: true
        },
        orderBy: {
          created_at: 'asc'
        },
        take: 5000 // 减少最大消息数量
      })
    } catch (error) {
      logger.warn(`获取客户 ${chatId} 聊天历史失败:`, error)
      // 如果获取失败，使用空数组继续处理
      messages = []
    }

    // 初始化客户数据
    const customerData: CustomerIntentData = {
      chatId,
      userId,
      courseNo: chat.course_no || 0,
      userName: chat.contact.wx_name,

      // 初始化对话轮次
      day0ConversationRounds: 0,
      day1ConversationRounds: 0,
      day2ConversationRounds: 0,
      day3ConversationRounds: 0,
      day4ConversationRounds: 0,

      // 初始化课程参与度
      day1CourseDuration: 0,
      day2CourseDuration: 0,
      day3CourseDuration: 0,
      day4CourseDuration: 0,

      // 初始化发言活跃度
      day1LiveComments: 0,
      day2LiveComments: 0,
      day3LiveComments: 0,
      day4LiveComments: 0,

      // 初始化作业完成情况
      day1Homework: state.is_complete_homework1 ?? false,
      day2Homework: state.is_complete_homework2 ?? false,
      day3Homework: state.is_complete_homework3 ?? false,
      day4Homework: state.is_complete_homework4 ?? false,

      // 初始化分数
      day1IntentScore: 0,
      day2IntentScore: 0,
      day3IntentScore: 0,
      day4IntentScore: 0,

      maxIntentScore: 0,
      currentIntentLevel: IntentLevel.ZERO
    }

    // 计算每天对话轮次
    for (let day = 0; day <= 4; day++) {
      const dayMessages = await Promise.all(messages.map(async (m) => {
        try {
          const scheduleTime = await DataService.getCurrentTimeByDate(chatId, m.created_at)
          return scheduleTime.day === day ? m : null
        } catch (error) {
          // 忽略单个消息的时间计算错误
          return null
        }
      }))
      const filteredMessages = dayMessages.filter((m) => m !== null && m.role === 'user')

      switch (day) {
        case 0:
          customerData.day0ConversationRounds = filteredMessages.length
          break
        case 1:
          customerData.day1ConversationRounds = filteredMessages.length
          break
        case 2:
          customerData.day2ConversationRounds = filteredMessages.length
          break
        case 3:
          customerData.day3ConversationRounds = filteredMessages.length
          break
        case 4:
          customerData.day4ConversationRounds = filteredMessages.length
          break
      }
    }

    // 处理课程信息
    if (courseInfo) {
      for (const info of courseInfo) {
        const day = info.day
        const liveId = info.liveId

        // 获取直播信息和消息
        const [liveInfo, recordInfo, messages] = await Promise.all([
          xingyanApi.getLiveStreamWatchTime(`${liveId}`, chat.phone ?? undefined).catch(() => 0),
          xingyanApi.getRoomRecordingWatchingTime(`${liveId}`, chat.phone ?? undefined).catch(() => 0),
          xingyanApi.getRoomMsgPage(`${liveId}`, chat.phone ?? undefined).catch(() => [])
        ])

        const totalDuration = (liveInfo || 0) + (recordInfo || 0)
        const commentsCount = messages?.length || 0

        switch (day) {
          case 1:
            customerData.day1CourseDuration = totalDuration
            customerData.day1LiveComments = commentsCount
            break
          case 2:
            customerData.day2CourseDuration = totalDuration
            customerData.day2LiveComments = commentsCount
            break
          case 3:
            customerData.day3CourseDuration = totalDuration
            customerData.day3LiveComments = commentsCount
            break
          case 4:
            customerData.day4CourseDuration = totalDuration
            customerData.day4LiveComments = commentsCount
            break
        }
      }
    }

    // 使用同期统计数据计算意向度分数
    const day2BeforeConversationRounds = customerData.day0ConversationRounds + customerData.day1ConversationRounds

    // Day1 意向度分数
    const day1ConversationScore = scoreConversation(
      Math.min(day2BeforeConversationRounds, cohortStats.q95Day2Before),
      cohortStats.q95Day2Before
    )
    const day1CompletionPercentage = Math.min(customerData.day1CourseDuration / 60, FEATURE_CONFIG.lecture1Length) / FEATURE_CONFIG.lecture1Length
    const day1CommentActivityZScore = cohortStats.day1CommentStats.std > 0 ?
      sigmoid(calculateZScore(customerData.day1LiveComments, cohortStats.day1CommentStats.mean, cohortStats.day1CommentStats.std)) : 0.5
    const day1CourseEngagement = FEATURE_CONFIG.lecWeight * day1CompletionPercentage + (1 - FEATURE_CONFIG.lecWeight) * day1CommentActivityZScore
    const day1CourseEngagementScore = day1CourseEngagement * 100
    const day1HomeworkScore = customerData.day1Homework ? 100 : FEATURE_CONFIG.hwBaseScore

    customerData.day1IntentScore = (
      FEATURE_CONFIG.conversationWeight * day1ConversationScore +
        FEATURE_CONFIG.lectureEngagementWeight * day1CourseEngagementScore +
        FEATURE_CONFIG.hwWeight * day1HomeworkScore
    )

    // Day2 意向度分数
    const day2ConversationScore = scoreConversation(
      Math.min(customerData.day2ConversationRounds, cohortStats.q95Day2),
      cohortStats.q95Day2
    )
    const day2CompletionPercentage = Math.min(customerData.day2CourseDuration / 60, FEATURE_CONFIG.lecture2Length) / FEATURE_CONFIG.lecture2Length
    const day2CommentActivityZScore = cohortStats.day2CommentStats.std > 0 ?
      sigmoid(calculateZScore(customerData.day2LiveComments, cohortStats.day2CommentStats.mean, cohortStats.day2CommentStats.std)) : 0.5
    const day2CourseEngagement = FEATURE_CONFIG.lecWeight * day2CompletionPercentage + (1 - FEATURE_CONFIG.lecWeight) * day2CommentActivityZScore
    const day2CourseEngagementScore = day2CourseEngagement * 100
    const day2HomeworkScore = customerData.day2Homework ? 100 : FEATURE_CONFIG.hwBaseScore

    customerData.day2IntentScore = (
      FEATURE_CONFIG.conversationWeight * day2ConversationScore +
        FEATURE_CONFIG.lectureEngagementWeight * day2CourseEngagementScore +
        FEATURE_CONFIG.hwWeight * day2HomeworkScore
    )

    // Day3 意向度分数
    const day3ConversationScore = scoreConversation(
      Math.min(customerData.day3ConversationRounds, cohortStats.q95Day3),
      cohortStats.q95Day3
    )
    const day3CompletionPercentage = Math.min(customerData.day3CourseDuration / 60, FEATURE_CONFIG.lecture3Length) / FEATURE_CONFIG.lecture3Length
    const day3CommentActivityZScore = cohortStats.day3CommentStats.std > 0 ?
      sigmoid(calculateZScore(customerData.day3LiveComments, cohortStats.day3CommentStats.mean, cohortStats.day3CommentStats.std)) : 0.5
    const day3CourseEngagement = FEATURE_CONFIG.lecWeight * day3CompletionPercentage + (1 - FEATURE_CONFIG.lecWeight) * day3CommentActivityZScore
    const day3CourseEngagementScore = day3CourseEngagement * 100
    const day3HomeworkScore = customerData.day3Homework ? 100 : FEATURE_CONFIG.hwBaseScore

    customerData.day3IntentScore = (
      FEATURE_CONFIG.conversationWeight * day3ConversationScore +
        FEATURE_CONFIG.lectureEngagementWeight * day3CourseEngagementScore +
        FEATURE_CONFIG.hwWeight * day3HomeworkScore
    )

    // Day4 意向度分数
    const day4ConversationScore = scoreConversation(
      Math.min(customerData.day4ConversationRounds, cohortStats.q95Day4),
      cohortStats.q95Day4
    )
    const day4CompletionPercentage = Math.min(customerData.day4CourseDuration / 60, FEATURE_CONFIG.lecture4Length) / FEATURE_CONFIG.lecture4Length
    const day4CommentActivityZScore = cohortStats.day4CommentStats.std > 0 ?
      sigmoid(calculateZScore(customerData.day4LiveComments, cohortStats.day4CommentStats.mean, cohortStats.day4CommentStats.std)) : 0.5
    const day4CourseEngagement = FEATURE_CONFIG.lecWeight * day4CompletionPercentage + (1 - FEATURE_CONFIG.lecWeight) * day4CommentActivityZScore
    const day4CourseEngagementScore = day4CourseEngagement * 100
    const day4HomeworkScore = customerData.day4Homework ? 100 : FEATURE_CONFIG.hwBaseScore

    customerData.day4IntentScore = (
      FEATURE_CONFIG.conversationWeight * day4ConversationScore +
        FEATURE_CONFIG.lectureEngagementWeight * day4CourseEngagementScore +
        FEATURE_CONFIG.hwWeight * day4HomeworkScore
    )

    // 计算最高意向度分数
    customerData.maxIntentScore = Math.max(
      customerData.day1IntentScore,
      customerData.day2IntentScore,
      customerData.day3IntentScore,
      customerData.day4IntentScore
    )

    // 确定意向度等级
    customerData.currentIntentLevel = getIntentLevel(customerData.maxIntentScore)

    // 分析意向度变化
    customerData.intentChanges = analyzeIntentChanges(customerData)

    return customerData
  } catch (error) {
    logger.error(`计算客户意向度分数失败 (chatId: ${chatId}):`, error)
    return null
  }
}

/**
 * 更新客户备注
 */
export async function updateCustomerRemark(chatId: string, intentLevel: IntentLevel): Promise<boolean> {
  try {
    const userId = getUserId(chatId)
    // const botId = Config.setting.wechatConfig?.id
    const botId = getBotId(chatId)

    if (!botId) {
      logger.error('未找到机器人ID配置')
      return false
    }

    // 获取当前备注
    const customerInfo = await JuziAPI.getCustomerInfo(botId, userId)
    if (!customerInfo) {
      logger.error(`获取客户信息失败: ${chatId}`)
      return false
    }

    const currentRemark = customerInfo.remark || ''

    // 移除旧的意向度标签
    const remarkWithoutIntent = currentRemark.replace(/\[(高|中|低|零)意向\]/g, '').trim()

    // 添加新的意向度标签
    const newRemark = remarkWithoutIntent ? `${remarkWithoutIntent} [${intentLevel}]` : `[${intentLevel}]`

    // 更新备注
    const result = await JuziAPI.updateUserAlias(botId, userId, newRemark)

    if (result) {
      logger.log(`成功更新客户备注 (chatId: ${chatId}, 意向度: ${intentLevel})`)
      return true
    } else {
      logger.error(`更新客户备注失败 (chatId: ${chatId})`)
      return false
    }
  } catch (error) {
    logger.error(`更新客户备注异常 (chatId: ${chatId}):`, error)
    return false
  }
}

/**
 * 计算并更新单个客户的意向度备注
 */
export async function calculateAndUpdateCustomerIntent(chatId: string, cohortStats: CohortStatistics): Promise<boolean> {
  try {
    const customerData = await calculateCustomerIntentScore(chatId, cohortStats)
    if (!customerData) {
      return false
    }

    // 更新客户备注
    const success = await updateCustomerRemark(chatId, customerData.currentIntentLevel)

    // 同时更新 user_flags 中的意向度数据
    await updateIntentLevelFlags(chatId, customerData)

    if (success) {
      logger.log(`客户意向度更新成功: ${customerData.userName} - ${customerData.currentIntentLevel} (${customerData.maxIntentScore.toFixed(1)}分)`)
    }

    return success
  } catch (error) {
    logger.error(`计算并更新客户意向度失败 (chatId: ${chatId}):`, error)
    return false
  }
}

/**
 * 更新客户标志中的意向度数据
 */
async function updateIntentLevelFlags(chatId: string, customerData: CustomerIntentData): Promise<void> {
  try {
    await chatStateStoreClient.clearCache(chatId)
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)

    // 重置所有意向度标志
    state.intent_level_high = false
    state.intent_level_medium = false
    state.intent_level_low = false
    state.intent_level_zero = false

    // 设置对应的意向度标志
    switch (customerData.currentIntentLevel) {
      case IntentLevel.HIGH:
        state.intent_level_high = true
        break
      case IntentLevel.MEDIUM:
        state.intent_level_medium = true
        break
      case IntentLevel.LOW:
        state.intent_level_low = true
        break
      case IntentLevel.ZERO:
        state.intent_level_zero = true
        break
    }

    // 更新意向度变化相关标志
    if (customerData.intentChanges) {
      const changes = customerData.intentChanges

      // 重置意向度变化标志
      state.intent_change_increasing = false
      state.intent_change_decreasing = false
      state.intent_change_stable = false
      state.intent_change_volatile = false
      state.intent_change_significant = false
      state.intent_change_level = false

      // 设置趋势标志
      switch (changes.trend) {
        case '意向度提升':
          state.intent_change_increasing = true
          break
        case '意向度下降':
          state.intent_change_decreasing = true
          break
        case '意向度稳定':
          state.intent_change_stable = true
          break
        case '意向度波动':
          state.intent_change_volatile = true
          break
      }

      // 设置显著变化标志
      if (changes.significantChanges > 0) {
        state.intent_change_significant = true
      }

      // 设置等级变化标志
      if (changes.levelChanges > 0) {
        state.intent_change_level = true
      }
    }

    // 保存更新后的状态
    await chatStateStoreClient.update(chatId, { state })

    logger.log(`客户标志意向度数据更新成功: ${customerData.userName} - ${customerData.currentIntentLevel}`)
  } catch (error) {
    logger.error(`更新客户标志意向度数据失败 (chatId: ${chatId}):`, error)
  }
}

/**
 * 按期数批量更新客户意向度备注 - 优化内存使用
 */
export async function batchUpdateCustomersIntentByCourseNo(courseNo: number): Promise<{
  total: number
  success: number
  failed: number
}> {
  try {
    const mongoClient = PrismaMongoClient.getInstance()

    // 获取指定期数的所有客户数量
    const customerCount = await mongoClient.chat.count({
      where: {
        course_no: courseNo,
        phone: {
          not: null
        }
      }
    })

    if (customerCount === 0) {
      logger.log(`期数 ${courseNo} 没有找到客户`)
      return { total: 0, success: 0, failed: 0 }
    }

    logger.log(`开始更新期数 ${courseNo} 的 ${customerCount} 个客户意向度`)

    // 计算同期统计数据
    const cohortStats = await calculateCohortStatistics(courseNo)

    let success = 0
    let failed = 0

    // 大幅减少批次大小，降低内存使用
    const batchSize = 50 // 从20减少到10
    const totalBatches = Math.ceil(customerCount / batchSize)

    // 限制并发数，避免内存溢出
    const limit = pLimit(20) // 从5减少到3

    for (let batch = 0; batch < totalBatches; batch++) {
      const skip = batch * batchSize

      const customers = await mongoClient.chat.findMany({
        where: {
          course_no: courseNo,
          phone: {
            not: null
          }
        },
        select: {
          id: true
        },
        skip,
        take: batchSize
      })

      const currentBatch = batch + 1
      logger.log(`处理批次 ${currentBatch}/${totalBatches}，客户 ${skip + 1}-${skip + customers.length}`)

      // 使用限制并发的方式处理批次
      const batchPromises = customers.map((customer) => {
        return limit(async () => {
          try {
            return await calculateAndUpdateCustomerIntent(customer.id, cohortStats)
          } catch (error) {
            logger.error(`处理客户 ${customer.id} 失败:`, error)
            return false
          }
        })
      })

      const batchResults = await Promise.allSettled(batchPromises)

      batchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          success++
        } else {
          failed++
        }
      })

      // 增加批次间延迟，给GC更多时间
      if (batch < totalBatches - 1) {
        if (global.gc) {
          global.gc()
          logMem(`batch ${batch + 1} after GC`)
        }
        await new Promise((resolve) => setTimeout(resolve, 2000)) // 从500ms增加到2000ms
      }
    }

    logger.log(`期数 ${courseNo} 意向度更新完成: 成功 ${success} 个，失败 ${failed} 个，总计 ${customerCount} 个`)

    return {
      total: customerCount,
      success,
      failed
    }
  } catch (error) {
    logger.error(`按期数批量更新客户意向度失败 (courseNo: ${courseNo}):`, error)
    return {
      total: 0,
      success: 0,
      failed: 0
    }
  }
}

/**
 * 根据当前时间更新所有需要更新的客户意向度
 */
export async function updateAllCustomersIntentByCurrentTime(): Promise<{
  total: number
  success: number
  failed: number
}> {
  try {
    const currentDate = dayjs()
    const currentCourseNo = parseInt(currentDate.format('YYYYMMDD'), 10)

    logger.log(`开始根据当前时间 ${currentDate.format('YYYY-MM-DD HH:mm:ss')} 更新客户意向度`)

    // 收集需要更新的期数
    const courseNosToUpdate: number[] = []

    // 检查最近5期的客户（从当前期数往前推4期）
    for (let i = 0; i < 5; i++) {
      const courseNo = currentCourseNo - i

      // 检查这期客户是否已经开始上课
      const courseStartDate = dayjs(courseNo.toString(), 'YYYYMMDD')
      const daysSinceStart = currentDate.diff(courseStartDate, 'day')

      // 如果已经开始上课，加入更新列表
      if (daysSinceStart >= 1) {
        courseNosToUpdate.push(courseNo)
      } else {
        logger.log(`期数 ${courseNo} 的客户还没开始上课，跳过`)
      }
    }

    if (courseNosToUpdate.length === 0) {
      logger.log('没有需要更新的期数')
      return { total: 0, success: 0, failed: 0 }
    }

    logger.log(`需要更新的期数: ${courseNosToUpdate.join(', ')}`)

    // 串行处理多个期数，避免内存累积
    let totalSuccess = 0
    let totalFailed = 0
    let totalCustomers = 0

    for (const courseNo of courseNosToUpdate) {
      try {
        logger.log(`开始处理期数 ${courseNo}`)
        const result = await batchUpdateCustomersIntentByCourseNo(courseNo)
        logger.log(`期数 ${courseNo} 处理完成: ${result.success}/${result.total} 成功`)

        totalSuccess += result.success
        totalFailed += result.failed
        totalCustomers += result.total

        // 期数间延迟，给GC时间
        if (courseNo !== courseNosToUpdate[courseNosToUpdate.length - 1]) {
          await new Promise((resolve) => setTimeout(resolve, 5000))
        }
      } catch (error) {
        logger.error(`期数 ${courseNo} 处理失败:`, error)
      }
    }

    logger.log(`所有期数意向度更新完成: 总计 ${totalCustomers} 个客户，成功 ${totalSuccess} 个，失败 ${totalFailed} 个`)
    console.log('本次任务最大heapUsed:', (maxHeapUsed / 1024 / 1024).toFixed(2), 'MB')
    return {
      total: totalCustomers,
      success: totalSuccess,
      failed: totalFailed
    }
  } catch (error) {
    logger.error('根据当前时间更新客户意向度失败:', error)
    return {
      total: 0,
      success: 0,
      failed: 0
    }
  }
}

let maxHeapUsed = 0
function logMem(tag = '') {
  const mem = process.memoryUsage()
  if (mem.heapUsed > maxHeapUsed) maxHeapUsed = mem.heapUsed
  console.log(`[${tag}] heapUsed: ${(mem.heapUsed / 1024 / 1024).toFixed(2)}MB, rss: ${(mem.rss / 1024 / 1024).toFixed(2)}MB`)
}

/**
 * 分析意向度变化
 */
function analyzeIntentChanges(customerData: CustomerIntentData) {
  // 计算每天之间的变化
  const day1ToDay2 = customerData.day2IntentScore - customerData.day1IntentScore
  const day2ToDay3 = customerData.day3IntentScore - customerData.day2IntentScore
  const day3ToDay4 = customerData.day4IntentScore - customerData.day3IntentScore

  const changes = [day1ToDay2, day2ToDay3, day3ToDay4]
  const maxChange = Math.max(...changes)
  const minChange = Math.min(...changes)
  const averageChange = changes.reduce((sum, change) => sum + change, 0) / changes.length

  // 判断趋势
  let trend: '意向度提升' | '意向度下降' | '意向度稳定' | '意向度波动'
  const positiveChanges = changes.filter((c) => c > 0).length
  const negativeChanges = changes.filter((c) => c < 0).length

  if (Math.abs(averageChange) < 5) {
    trend = '意向度稳定'
  } else if (positiveChanges > negativeChanges && averageChange > 10) {
    trend = '意向度提升'
  } else if (negativeChanges > positiveChanges && averageChange < -10) {
    trend = '意向度下降'
  } else {
    trend = '意向度波动'
  }

  // 计算显著变化次数（变化幅度超过10分）
  const significantChanges = changes.filter((c) => Math.abs(c) >= 10).length

  // 计算等级变化次数
  const levelChanges = changes.filter((c) => {
    // 简单的等级变化判断：变化幅度超过15分
    return Math.abs(c) >= 20
  }).length

  return {
    day1ToDay2,
    day2ToDay3,
    day3ToDay4,
    maxChange,
    minChange,
    averageChange,
    trend,
    significantChanges,
    levelChanges
  }
}

/**
 * 示例：如何使用意向度变化分析功能
 */
export async function exampleIntentChangeAnalysis(chatId: string): Promise<void> {
  try {
    // 1. 获取客户期数
    const mongoClient = PrismaMongoClient.getInstance()
    const chat = await mongoClient.chat.findUnique({
      where: { id: chatId },
      select: { course_no: true }
    })

    if (!chat?.course_no) {
      logger.warn(`客户 ${chatId} 没有期数信息`)
      return
    }

    // 2. 计算同期统计数据
    const cohortStats = await calculateCohortStatistics(chat.course_no)

    // 3. 计算客户意向度（包含变化分析）
    const customerData = await calculateCustomerIntentScore(chatId, cohortStats)

    if (!customerData) {
      logger.warn(`无法计算客户 ${chatId} 的意向度数据`)
      return
    }

    // 4. 分析意向度变化
    if (customerData.intentChanges) {
      const changes = customerData.intentChanges

      logger.log(`客户 ${customerData.userName} 意向度变化分析:`)
      logger.log(`- Day1→Day2: ${changes.day1ToDay2.toFixed(1)}分`)
      logger.log(`- Day2→Day3: ${changes.day2ToDay3.toFixed(1)}分`)
      logger.log(`- Day3→Day4: ${changes.day3ToDay4.toFixed(1)}分`)
      logger.log(`- 平均变化: ${changes.averageChange.toFixed(1)}分`)
      logger.log(`- 趋势: ${changes.trend}`)
      logger.log(`- 显著变化次数: ${changes.significantChanges}`)
      logger.log(`- 等级变化次数: ${changes.levelChanges}`)

      // 5. 根据变化情况生成建议
      if (changes.trend === '意向度下降') {
        logger.warn(`客户 ${customerData.userName} 意向度呈下降趋势，建议及时跟进`)
      } else if (changes.trend === '意向度提升') {
        logger.log(`客户 ${customerData.userName} 意向度呈上升趋势，转化机会良好`)
      } else if (changes.significantChanges > 0) {
        logger.log(`客户 ${customerData.userName} 有意向度显著变化，需要关注`)
      }
    }

    // 6. 更新客户状态标志
    await updateIntentLevelFlags(chatId, customerData)

  } catch (error) {
    logger.error(`意向度变化分析示例失败 (chatId: ${chatId}):`, error)
  }
}