import dayjs from 'dayjs'
import { Config } from 'config'
import { JuziAPI } from 'model/juzi/api'
import logger from 'model/logger/logger'
import { startGroupTasks } from 'service/visualized_sop/visualized_group_sop_task_starter'
import { calGroupTaskTime, DataService } from '../helper/getter/get_data'
import { accountToName } from '../config/config'
import { RedisDB } from 'model/redis/redis'
import { AddUserToGroup } from '../helper/group/add_user_to_group'
import { PrismaMongoClient } from '../database/prisma'
import { loadConfigByWxId } from 'model/bot_config/load_config'

export const groupConditionJudgeMap:Record<string, ((params:{groupId:string})=>Promise<boolean>)> = {
}

export const groupTextVariableMap:Record<string, (params:{groupId:string})=> Promise<string>> = {
  '第一节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 1, Config.getYuHeAccountType(groupInfo.owner_account_id)) ?? ''
  },
  '第二节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 2, Config.getYuHeAccountType(groupInfo.owner_account_id)) ?? ''
  },
  '第三节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 3, Config.getYuHeAccountType(groupInfo.owner_account_id)) ?? ''
  },
  '第四节课链接': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行sop的时候没有找到这个群 group_id: ${groupId}`)
      return ''
    }
    return await DataService.getCourseLinkByCourseNo(groupInfo.course_no, 4, Config.getYuHeAccountType(groupInfo.owner_account_id)) ?? ''
  }
}

export const groupActionCustomMap:Record<string, (params:{groupId:string})=> Promise<void>> = {
  '踢人': async({ groupId }) => {
    const mongoClient = PrismaMongoClient.getInstance()
    const groupInfo = await mongoClient.group.findFirst({ where:{ group_id:groupId } })
    if (!groupInfo) {
      logger.error(`执行踢人sop的时候没有找到这个群 group_id: ${groupId}`)
      return
    }
    const botConfig = await loadConfigByWxId(groupInfo.owner_account_id)
    const imBotId = botConfig.id
    const botUserId = botConfig.botUserId
    const whitelist = ['****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', '****************', ...Object.keys(accountToName)]
    await JuziAPI.kickNonWhitelistMembers(botUserId, imBotId, groupId, whitelist)
    await mongoClient.group.update({ where:{ group_id:groupId }, data:{ course_no:Number(dayjs(String(groupInfo.course_no), 'YYYYMMDD').add(4, 'day').format('YYYYMMDD')) } })
    const redisClient = RedisDB.getInstance()
    await redisClient.set(AddUserToGroup.getGroupNumberRedisKey(groupId), '0')
    await startGroupTasks(PrismaMongoClient.getInstance(), 'yuhe', groupId, calGroupTaskTime)
  }
}

export const groupLinkSourceVariableTagMap:Record<string, (params:{groupId:string})=>Promise<string>> = {
}
