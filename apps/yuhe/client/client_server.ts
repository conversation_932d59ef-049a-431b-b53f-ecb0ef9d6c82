import express from 'express'
import axios from 'axios'
import logger from 'model/logger/logger'
import { exec } from 'child_process'
import { IReceivedMessage, ISendMessageResult } from 'model/juzi/type'
import { JuziAPI } from 'model/juzi/api'
import { CacheDecorator } from 'lib/cache/cache'
import { JuziMessageHandler } from 'service/message_handler/juzi/message_handler'
import { initConfig } from './init'
import { catchGlobalError } from 'model/server/server'
import { EventHandler, sendYuHeWelComeMessage } from './event_handler'
import { IXingyanPushCallback } from 'model/xingyan'
import { ITestEventChangeNextStage, ITestEventClearCache } from './event_type'
import { handleImageMessage, handleUnknownMessage, handleVideoMessage } from './message_handler'
import { sendMessageResultHandlerClient } from '../service/instance'
import { PrismaMongoClient } from '../database/prisma'
import { messageReplyServiceClient } from '../service/message_replay_service_instance'
import { receiveMessageCount } from '../prometheus/client'
import { register } from 'lib/prometheus_client/index'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { eventTrackClient } from '../service/event_track_instance'

const app = express()
const messageHandler = new JuziMessageHandler({
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage,
  sendWelcomeMessage:sendYuHeWelComeMessage
},
chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient)

app.use(express.json())

app.get('/', (req, res) => {
  res.send('Hello Client!')
})

app.post('/message', async (req, res) => {
  // 接收消息
  const msg: IReceivedMessage = req.body

  messageHandler.handle(msg) // 添加到消息队列
  receiveMessageCount.labels({ bot_id: msg.imBotId }).inc(1)
  res.send('ok')
})

app.get('/metrics', async (req, res) => {
  res.status(200).set('Content-Type', register.contentType).send(await register.metrics())
})

app.post('/event', async (req, res) => {
  // 接收消息
  const data = req.body

  new EventHandler().handle(data)
  res.send('ok')
})

app.post('/sendResult', async (req, res) => {
  // 接收消息
  const data: ISendMessageResult = req.body

  sendMessageResultHandlerClient.handle(data) // 处理消息发送结果
  res.send('ok')
})

app.post('/yuhe/event', async (req, res) => {
  new EventHandler().handleYuHeEvent(req.body as IXingyanPushCallback<any>)
  res.send('ok')
})

app.post('/test/event/change_stage', async (req, res) => {
  const data = req.body as ITestEventChangeNextStage

  await chatStateStoreClient.update(data.chatId, {
    nextStage: data.stage
  })

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/clear_cache', async(req, res) => {
  const data = req.body as ITestEventClearCache

  await chatStateStoreClient.clearCache(data.chatId)

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event/handle_invite_group_fail', async(req, res) => {
  const chatsJson = await PrismaMongoClient.getInstance().chat.findRaw({
    filter: {
      'chat_state.state.is_invite_group_fail_after_payment': true,
    },
  })
  //TODO:注意只需要发送一个请求即可，不需要每个服务器发送一个

  if (!chatsJson) {
    return res.send({
      code: 500,
      msg: '无法找到未拉群客户'
    })
  }

  const chats = chatsJson as unknown as any[]

  const failRes: string[] =  []

  for (let i = 0; i < chats.length; i++) {
    try {
      await EventHandler.inviteToGroup(chats[i]._id)
      const chatState = await chatStateStoreClient.get(chats[i]._id)
      chatState.state.is_invite_group_fail_after_payment = false
      await chatDBClient.updateState(chats[i]._id, chatState)
      await chatStateStoreClient.clear(chats[i]._id)
    } catch (e) {
      failRes.push(chats[i]._id)
    }
  }

  if (failRes.length === 0) {
    res.send({
      code: 200,
      msg: '拉群成功'
    })
  } else {
    res.send({
      code: 200,
      msg: '拉群部分成功'
    })
  }

})

// 缓存 API，提高性能
// JuziAPI.getCustomerInfo = CacheDecorator.decorateAsync(JuziAPI.getCustomerInfo)
JuziAPI.externalIdToWxId = CacheDecorator.decorateAsync(JuziAPI.externalIdToWxId)

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  await initConfig()

  // 消息处理 Worker
  messageHandler.startWorker()

  app.listen(8001, '0.0.0.0', () => {
    console.log(`Server is running on port ${8001}`)
  })
}