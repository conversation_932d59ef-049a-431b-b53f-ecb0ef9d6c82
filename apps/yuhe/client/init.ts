import { loadConfigByAccountName } from 'service/database/config'
import chalk from 'chalk'
import { Config } from 'config'
import { TaskRegister } from '../schedule/register_task'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { yuheVisualizedGroupSopProcessor, yuheVisualizedSopProcessor } from '../service/instance'

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = '老师'
  Config.setting.projectName = 'yuhe'

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  // SOP Worker
  yuheVisualizedSopProcessor.start()
  yuheVisualizedGroupSopProcessor.start()

  // Config.setting.onlyReceiveMessage = true
}