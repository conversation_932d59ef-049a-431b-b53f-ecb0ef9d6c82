import { getUserId } from 'config/chat_id'
import { checkRobotDetection } from 'service/agent/utils'
import { PrismaMongoClient } from '../../database/prisma'
import { chatStateStoreClient } from '../../service/base_instance'
import { humanTransferClient } from '../../service/human_transfer_instance'

describe('Test', function () {
  beforeAll(() => {

  })

  it('checkRobotDetection', async () => {
    const chat_id = '7881302298050442_1688857404698934'
    const user_id = getUserId(chat_id)
    const userMessage = '老师，你他妈就是AI吧'
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chat_id, '', user_id, userMessage)
    console.log('isRobotDetection', isRobotDetection)
  }, 60000)

})
