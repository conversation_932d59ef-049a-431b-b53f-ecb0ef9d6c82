import { IWorkflowState } from 'service/llm/state'
import { ChatState } from 'service/local_cache/chat_state'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../../schedule/type'
import { Config } from 'config'
import { sleep } from 'openai/core'
import logger from 'model/logger/logger'
import { StringHelper } from 'lib/string'
import { IEventType } from 'model/logger/data_driven'
import { ContextBuilder } from '../context'
import { replyClient } from '../../service/instance'
import { chatStateStoreClient } from '../../service/base_instance'
import { eventTrackClient } from '../../service/event_track_instance'
import { Node, trackInvoke, WorkFlowNode } from 'service/agent/workflow'

export class Homework2Node extends WorkFlowNode {
  @trackInvoke
  public static async invoke(state: IWorkflowState):Promise<Node> {
    await Homework2Store.addUserMessage(state.chat_id, state.userMessage)

    // 加个兜底逻辑，5 分钟后去把队列清空一下
    await SilentReAsk.schedule(
      TaskName.homework_cleanup,
      state.chat_id,
      5 * 60 * 1000,
      { storeType: 'homework2' }
    )

    if (!Config.setting.localTest) {
      await sleep(3 * 60 * 1000)
    }

    const homework1Messages = await Homework2Store.getUserMessages(state.chat_id)
    if (homework1Messages.indexOf(state.userMessage) !== homework1Messages.length - 1) { // 只有最后一条才进行处理，其他的当做打断处理
      logger.warn({ chat_id: state.chat_id }, '接收到新的Homework1消息，被合并到当前轮Homework1消息处理，本条消息不再做处理')
      return Node.FreeTalk
    }

    // 对客户作业消息进行合并处理， 并移除 比如👉： 之前的字段
    const userHomework = homework1Messages.map((message) =>  StringHelper.removeStringBeforePrefix('👉：', message)).join('\n')

    // 清空 wealthOrchardMessages
    await Homework2Store.clearUserMessages(state.chat_id)

    const context = await ContextBuilder.build({
      state,
      customerChatRounds: 0,
      customerPortrait: true,
      customPrompt: `# 作业点评
- 你需要根据客户作业：客户身份、你打算做什么、具体要求三个方面分别帮助客户进行诊断，在这个过程中你应该首先肯定客户的身份和目标，以及具体要求实现有一定的难度，但是跟着中神通老师好好听课学习还是有机会做到的

## 作业模版
【第二课作业】
你的身份（我是在哪里做XX的）：
你打算做什么（我想通过短视频吸引更多客户到店）：
提出具体要求（请根据目前抖音比较火的XX，帮我创作XX）：
【按照以上模板在里面填写，老师会给你一对一诊断】

## 点评方向
- 先肯定客户的作业，然后提出问题点，最后丝滑引导到中神通第三课课程内容：教你如何搭建自己的一个矩阵系统，以及我们的直播该如何去做，该怎么去搭建我们的直播间

## 示例1
客户作业
  你的身份：我是杭州做皮肤管理的
  你打算做什么：我想通过短视频展示项目优势吸引客户到店
  提出具体要求：请帮我设计几个视频讲解思路
思考过程：客户目标明确，有一定运营意识，但还停留在内容拍摄维度，对账号矩阵、直播互动等关键动作认知薄弱。需要借助课程将客户从“内容想法”带入“系统打法”
输出结果：你这个方向很好👍皮肤管理做展示型内容确实能吸引爱美客户，但仅靠几条短视频是不够持续获客的。现在很多同行已经开始用AI矩阵+直播场景+数字人导流系统在抖音上爆单，第三课中神通老师的课程就是手把手教你怎么做这个系统，帮你从内容变现走到成交闭环。记得笔记要做好！

## 示例2
客户作业
  你的身份：我是长沙开花店的
  你打算做什么：我想通过抖音直播卖节日鲜花
  提出具体要求：请帮我设计直播间布景和话术节奏
思考过程：花店本地类，适合通过直播引导下单，客户目标对路，但提出的内容还只是“表层执行点”，对背后的引流系统、直播脚本框架还不了解。此时需要通过课程引导认知升级。
输出结果：你能想到直播卖花，很有嗅觉但只关注布景和话术，说明你还没意识到直播真正成交靠的是整套系统：账号矩阵+引流视频+直播脚本+人设设计。第三课中神通老师就教这个，尤其适合你这种本地消费类店铺！想真正卖爆节日鲜花，这节课你一定要盯紧看！

## 示例3
客户作业
  我是西安开母婴店的，想用直播带动线下客流和会员办卡，请教我如何安排一场直播流程
思考过程：客户已经具备“直播+线下转化”的思路，说明认知层级较高，但尚未具备对“系统搭建、矩阵运营”的统筹概念。要强调课程的系统性价值。
输出结果：你这个思路走得很对，母婴客户高粘性，用直播带动线下是绝佳组合但直播流程不是“单场”，而是需要账号矩阵、选品脚本、人设话术、直播间动线一体化配合。第三课中神通老师讲的课程，就是教你从0搭建直播系统，实现稳定转化。这对你门店未来的获客节奏影响巨大，今天内容别漏一秒！

## 输出要求
- 输出一段话，口语化表达，结果尽量保持在200字以内
- 直接输出回答客户的结果，不要输出思考过程或其他内容
- 如果客户发言是作业内容，则按照要求点评，反之就正常回复客户,

## 客户作业
${userHomework}`,
    })

    await replyClient.invoke({
      state,
      context: context,
      promptName: 'homework2',
      noInterrupt: true,
      noSplit: true,
    })
    eventTrackClient.track(state.chat_id, IEventType.HomeworkComplete, { 'day': 2 })
    await chatStateStoreClient.update(state.chat_id, { state:{ is_complete_homework2: true } })

    return Node.FreeTalk
  }
}
export class Homework2Store {
  private static userMessages = new ChatState<string[]>('yuhe', 'homework2')  // 对客户的消息进行暂存

  public static async addUserMessage(chat_id: string, message: string) {
    const messages = await this.userMessages.get(chat_id) || []
    messages.push(message)
    await this.userMessages.set(chat_id, messages)
  }

  public static async getUserMessages(chat_id: string) {
    return await this.userMessages.get(chat_id) || []
  }

  public static async clearUserMessages(chat_id: string) {
    await this.userMessages.set(chat_id, [])
  }
}

export class HomeworkDurationUserMessageStore {
  private static userMessages = new ChatState<string[]>('yuhe', 'homework_duration_user_message')  // 对客户的消息进行暂存

  public static async addUserMessage(chat_id: string, message: string) {
    const messages = await this.userMessages.get(chat_id) || []
    messages.push(message)
    await this.userMessages.set(chat_id, messages)
  }

  public static async getUserMessages(chat_id: string) {
    return await this.userMessages.get(chat_id) || []
  }

  public static async clearUserMessages(chat_id: string) {
    await this.userMessages.set(chat_id, [])
  }
}