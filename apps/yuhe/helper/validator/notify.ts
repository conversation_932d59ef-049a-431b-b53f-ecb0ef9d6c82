import { GroupNotification } from 'service/group_notification/group_notification'
import { NotificationProvider, NotifyOptions } from 'service/human_transfer/human_transfer'

export class YuHeConfigNotify implements NotificationProvider {
  private static groupId = 'R:10815051791863856'
  private static imBotId = '1688857404698934'
  public static async notify(message: string) {
    await GroupNotification.notify(message, 'R:10933256292171603', '1688854546332791')
  }
  public async notify(message: string, options?: NotifyOptions):Promise<void> {
    await GroupNotification.notify(message, options?.groupId ?? YuHeConfigNotify.groupId, options?.imBotId ?? YuHeConfigNotify.imBotId)
  }
}