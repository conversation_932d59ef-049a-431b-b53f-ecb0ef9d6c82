import express from 'express'
import { IXingyanPushCallback, LiveRoomStatusChangeCallback, XingyanPushType } from 'model/xingyan'
import logger from 'model/logger/logger'
import { catchGlobalError } from 'model/server/server'
import { Config, YuHeAccountType } from 'config'
import { YuHeEventForwardHandler } from './event_forward'
import { RedisDB } from 'model/redis/redis'
import { Job, Worker } from 'bullmq'
import { DataService } from '../helper/getter/get_data'
import { YuHeValidator } from '../helper/validator/validator'
import { loadConfigByWxId } from 'model/bot_config/load_config'
import { DanmuAnalyzer } from '../danmu/danmu_analyzer'
import { updateAllCustomersIntentByCurrentTime } from '../signal_catcher/intent_score'
import { LatencyAnalyzer } from '../signal_catcher/latency_analyzer'

/**
 * 进行事件转发 和 每日导入白名单事件 处理
 */
const app = express()
app.use(express.json())

app.post('/yuhe/event', async (req, res) => {
  // 接收消息
  const data: IXingyanPushCallback<any> = req.body

  res.send({
    code: 200,
    msg: 'ok'
  })

  logger.log(JSON.stringify(data, null, 4))

  // 若是直播状态事件，直接处理，不转发
  if (data.type === XingyanPushType.LIVE_ROOM_STATUS_CHANGE) {
    await handleLiveRoomStatusChange(data as LiveRoomStatusChangeCallback, YuHeAccountType.ZhongShenTong)
    return
  }

  YuHeEventForwardHandler.forward(data) // 转发 Yuhe 事件
})


app.post('/yuhe/event/zhicheng', async (req, res) => {
  // 接收消息
  const data: IXingyanPushCallback<any> = req.body

  res.send({
    code: 200,
    msg: 'ok'
  })

  logger.log(JSON.stringify(data, null, 4))

  // 若是直播状态事件，直接处理，不转发
  if (data.type === XingyanPushType.LIVE_ROOM_STATUS_CHANGE) {
    await handleLiveRoomStatusChange(data as LiveRoomStatusChangeCallback, YuHeAccountType.ZhiCheng)
    return
  }

  YuHeEventForwardHandler.forward(data) // 转发 Yuhe 事件
})

catchGlobalError()


interface IYuHeLiveStreamConfigJobData {
  only_check?: boolean
  prevDay?: boolean // 前一天进行检查
}

// 启动每天导入白名单任务
// TODO:导入白名单问题
new Worker('yuhe_import_white_list', async (job: Job) => {

  // 是否只做校验通知
  const data = job.data as IYuHeLiveStreamConfigJobData

  // 1. 分组配置，校验直播间配置，进阶课配置
  await YuHeValidator.validateLiveStream(data.prevDay)

  if (data.only_check) {
    return
  }

  // 2. 导入白名单
  await DataService.importWhiteListOfYesterdayUsers()
}, {
  connection: RedisDB.getInstance()
})

// 客户意向度更新Worker - 监听主队列
new Worker('yuhe_intent_score_tag_update', async (job: Job) => {
  try {
    logger.log('开始执行客户意向度更新任务')

    const startTime = Date.now()
    const result = await updateAllCustomersIntentByCurrentTime()
    const endTime = Date.now()

    logger.log(`客户意向度更新任务完成，耗时: ${(endTime - startTime) / 1000}秒`)
    logger.log(`更新结果: 总计 ${result.total} 个客户，成功 ${result.success} 个，失败 ${result.failed} 个`)

    return result
  } catch (error) {
    logger.error('客户意向度更新任务执行失败:', error)
  }
}, {
  connection: RedisDB.getInstance(),
  concurrency: 1 // 只允许一个任务同时执行
}).on('error', (err) => {
  logger.error('创建意向度更新Worker发生未捕获错误', err)
})

new Worker('yuhe_latency_analyze', async(job: Job) => {
  const startTime = Date.now()
  const result = await LatencyAnalyzer.analyzeAndScheduleMsg()
  const endTime = Date.now()
  logger.log(`客户回复延迟分析&安排消息完成，耗时: ${(endTime - startTime) / 1000}秒`)
}, {
  connection: RedisDB.getInstance(),
  concurrency: 1
}).on('error', (err) => {
  logger.error('创建延迟分析Worker发生未捕获错误', err)
})


const serverPort = 5000
app.listen(serverPort, '0.0.0.0', async () => {
  console.log(`Server is running on port ${serverPort}`)
})

Config.setting.eventForward = true // 标记 事件转发服务
Config.setting.projectName = 'yuhe'

async function handleLiveRoomStatusChange(callback: LiveRoomStatusChangeCallback, accountType: YuHeAccountType) {
  logger.log('直播间状态变更:', JSON.stringify(callback, null, 4))
  logger.log(`账户类型: ${accountType}`)

  // 只处理直播结束 (=1) 的情况
  if (callback.param.status !== 1) return

  const roomId = callback.param.roomId.toString()
  logger.log(`直播间 ${roomId} 已结束，开始分析弹幕`) // 这里在日志库中能找到说明已经执行到这了

  try {
    const analyzer = new DanmuAnalyzer()
    await analyzer.analyzeUsersByRoomId(roomId, accountType) // 传递账户类型参数
  } catch (error) {
    logger.warn(`直播间 ${roomId} 弹幕分析失败:`, error)
  }
}