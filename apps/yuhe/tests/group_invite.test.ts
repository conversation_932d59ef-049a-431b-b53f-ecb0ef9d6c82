import { Config } from 'config'
import { loadConfigByAccountName } from 'service/database/config'
import { PrismaMongoClient } from '../database/prisma'
import { chatStateStoreClient, chatDBClient } from '../service/base_instance'


describe('groupInvite', () => {

  it('重新拉群脚本', async () => {

    Config.setting.projectName = 'yuhe'

    const chatsJson = await PrismaMongoClient.getInstance().chat.findRaw({
      filter: {
        'chat_state.state.is_invite_group_fail_after_payment': true,
      },
    })

    const chats = chatsJson as unknown as any[]

    const failRes: string[] =  []

    for (let i = 0; i < chats.length; i++) {
      try {
        // await YuHeEventHandler.inviteToGroup(chats[i]._id, getUserId(chats[i]._id))
        const chatState = await chatStateStoreClient.get(chats[i]._id)
        chatState.state.is_invite_group_fail_after_payment = false
        await chatDBClient.updateState(chats[i]._id, chatState)
      } catch (e) {
        failRes.push(chats[i]._id)
      }
    }
  }, 9e8)

  it('重置所有客户拉群失败状态', async () => {
    const chatsJson = await PrismaMongoClient.getInstance().chat.findRaw({
      filter: {
        'chat_state.state.is_invite_group_fail_after_payment': true,
      },
    })

    const chats = chatsJson as unknown as any[]

    for (let i = 0; i < chats.length; i++) {
      const chatState = await chatStateStoreClient.get(chats[i]._id)
      chatState.state.is_invite_group_fail_after_payment = false
      await chatDBClient.updateState(chats[i]._id, chatState)
    }
  }, 9e8)
})