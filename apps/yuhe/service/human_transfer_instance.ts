import { HumanTransfer, HumanTransferType } from 'service/human_transfer/human_transfer'
import { chatDBClient, chatStateStoreClient } from './base_instance'
import { eventTrackClient } from './event_track_instance'
import { YuHeConfigNotify } from '../helper/validator/notify'

export const humanTransferClient = new HumanTransfer({
  transferMessage: HumanTransferType,
  eventTracker: eventTrackClient,
  chatDB: chatDBClient,
  chatStateStore: chatStateStoreClient,
  notifier: new YuHeConfigNotify()
})
