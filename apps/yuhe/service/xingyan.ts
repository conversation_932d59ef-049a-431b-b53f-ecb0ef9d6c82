import { Config } from 'config'
import { XingyanAP<PERSON>, YuHeAccountType } from 'model/xingyan'

export const zhongShengTongXingyanApi = new XingyanAPI({
  accountType: YuHeAccountType.ZhongShenTong,
  cropId: Config.setting.xingyan.zhongshentong.cropId,
  secretKey: Config.setting.xingyan.zhongshentong.secretKey
})

export const zhiChengXingyanApi = new XingyanAPI({
  accountType: YuHeAccountType.ZhiCheng,
  cropId: Config.setting.xingyan.zhicheng.cropId,
  secretKey: Config.setting.xingyan.zhicheng.secretKey
})

export function getXingyanApiByBotId(botId: string) {
  const yuHeAccountType = Config.getYuHeAccountType(botId)
  if (yuHeAccountType == YuHeAccountType.ZhiCheng) {
    return zhiChengXingyanApi
  } else {
    return zhongShengTongXingyanApi
  }
}