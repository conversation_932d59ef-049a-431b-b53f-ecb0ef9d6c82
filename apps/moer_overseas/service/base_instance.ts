import { ChatHistoryService } from 'service/chat_history/chat_history'
import { ChatStateStore } from 'service/local_cache/chat_state_store'
import { ChatDB } from '../database/chat_db'
import { ChatDB as ChatCommonDB } from 'service/database/chat'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'
import { PrismaMongoClient } from '../helper/mongodb/prisma'
import { enterpriseName } from './global_data'


export const chatDBCommonClient = new ChatCommonDB(PrismaMongoCommonClient.newInstance(enterpriseName))
export const chatDBClient = new ChatDB(PrismaMongoClient.getInstance())
export const chatStateStoreClient = new ChatStateStore('moer_overseas', chatDBCommonClient)
export const chatHistoryServiceClient = new ChatHistoryService(PrismaMongoCommonClient.newInstance(enterpriseName), chatStateStoreClient)