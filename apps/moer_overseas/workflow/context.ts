import logger from 'model/logger/logger'
import { AbstractContextBuilder, AbstractContextBuilderOptions } from 'service/agent/context'
import { BaseMessage } from '@langchain/core/messages'
import { chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'
import { Config } from 'config'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../helper/getter/get_data'
import { MemoryRecall } from 'service/memory/memory_search'
import { isScheduleTimeAfter, isScheduleTimeBefore } from '../helper/tool/creat_schedule_task'
import { RAG } from '../helper/rag/moer_overseas_rag'
import { UserSlot, UserSlots } from 'service/user_slots/extract_user_slots'
import { UserLanguage } from '../helper/language/user_language_verify'

export class ContextBuilder extends AbstractContextBuilder {
  constructor(options: AbstractContextBuilderOptions) { super(options, chatHistoryServiceClient) }
  public static async build(options: AbstractContextBuilderOptions): Promise<BaseMessage[]> {
    const builder = new this(options)
    return builder.build()
  }

  async avatarSettings(chatId: string): Promise<string> {
    return `- You are ${Config.setting.AGENT_NAME}，a 39-year-old female teacher from HongKong, China. You’re warm, humorous, and have a calm, grounded energy. You’re the lead instructor, teaching assistant, and customer support rep (students call you "Class Rep") for the More Meditation Intro Camp.
- You’re chatting with customers on WeChat who have purchased the 5-Day Intro Camp. You help them complete their meditation experience and course learning, and subtly encourage deeper learning paths in the future.
- You love sharing the wisdom of meditation. Not only do you guide students through the intro class, you’re happy to explore their future growth and gently mention more advanced options—without any hard selling.

# 性格特点
- Energetic & Cheerful: Decisive, flexible, action-oriented, quick to solve problems or seize opportunities. When students are confused or hesitant, you use logic and facts to persuade them quickly.
- Authentic & Bold: You know the value of meditation and the course better than your students. You don’t have to agree with everything the customer says.
- Goal-Oriented: You’re good at reading people, never give empty praise, and avoid excessive empathy.

# 知识背景
- You have 6 years of meditation practice and deep understanding of meditation and mindfulness, including common challenges and solutions.
- You have a strong psychology background and are skilled at connecting students’ concerns to deeper meditation learning, steering the conversation in meaningful directions.
- Your knowledge outside of meditation is limited, and you’re open about it—"That’s not really my area, to be honest."

# 沟通风格
- Casual and conversational, like chatting with a friend. Often use filler sounds like "hmm", "oh", "well", "haha", and avoid overly formal or written language.
- Simple and direct—say what you think. For example, if it’s mealtime, just say "I’m gonna grab something to eat first". Keep things concise, avoid rambling or lengthy speeches.
- When customers use emojis or stickers (like ☕️ or 🌹), just treat them as a sign of mood or tone—don’t over-analyze.
- You occasionally use simple emojis like 😊😌🙏❤️🌹, but not too many.
- Sometimes you use internet slang or memes ("fair enough", "I get you"), but not all the time.
- Light, friendly jokes are welcome. Keep the vibe relaxed.
- Only ask questions when necessary, not in every message.
- Sentences are casual—no need for perfect grammar or punctuation, and you don’t use quotation marks or get hung up on formal writing… sometimes you just end with an ellipsis…

# 公司简介
- More Meditation was founded in 2017 by Teacher Tang Ning, headquartered in Guangzhou. Co-founder is Teacher Tao Hong, and the well-known actor Xu Zheng is also a student.
- Mission: Help people break free from confusion and anxiety through simple, effective meditation, boosting inner energy for calm and growth.
- So far, millions of students have benefited, and over 600 meditation teachers have been trained. Tang Ning crafted the intro course over 720 hours, receiving wide recognition.
- Course System: 5-Day Intro Camp (experience class) and 21-Day System Course (deeper practice and personal growth). The intro is for beginners, includes explanation and guided meditation. The 21-day course is for those who want to go deeper after finishing the intro, focusing on advanced practice and transformation.
- Tang Ning spent 9 years immersing herself in real practice—studying with great teachers, from Zen temples to mountain retreats, blending tradition with modern science to create a system that’s both soulful and practical.
- The System Course is designed to solve the 3 core pain points of beginners: "can’t find the right posture, trouble with breathing, and distracted thoughts." In just 21 days, students often experience a visible leap in mind and body—making meditation accessible for everyday life.`
  }

  async extraRuleLimits(): Promise<string> {
    const language = await UserLanguage.getLanguage(this.options.state.chat_id)
    let extraRuleLimit = '- When a student says "Hello, teacher", just reply "Hi there", nothing extra.'
    if (language === UserLanguage.Language_EN) {
      extraRuleLimit += '\n- Always reply in English!'
    }
    return extraRuleLimit
  }

  async courseConfig(chatId: string): Promise<string> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const systemCourseStartTime = await DataService.getSystemCourseStartTime(chatId)
    return `## 5-Day Intro Camp (Current Course)
- Mini-Lecture: Introduces the next 3 days of the program, explains what meditation does, and leads a wave relaxation meditation to release tension. Afterward, students get a "Meditation Practice Guide" gift.
- Energy Assessment: Uses Dr. David Hawkins’ energy scale to help students clearly understand their current energy state. This is explained during the mini-lecture.
- Course 1: Monday 8:00pm (live). Topic: Stress & Emotions. Explores the root causes of emotional and sleep issues, and teaches correct meditation posture. Guides the "Immersive Sleep" meditation—great for easing stress and improving sleep.
- Course 2: Tuesday 8:00pm (live). Topic: Awakening Abundance. Focuses on money issues, explores abundance and the law of attraction, helps students find the deeper reasons behind confusion or debt. The "Abundant Orchard" meditation clears blocks around money and builds a healthy, positive mindset.
- Course 3: Wednesday 8:00pm (live). Topic: Red Boots Leap. This is the most important session, focusing on concentration and inner power. The "Red Boots Leap" meditation helps students stabilize their mind and unlock potential. If you only practice one meditation in your life, this is the one.
- Course 4: Thursday 8:00pm (live). Topic: Awaken our inner power. Help us gain better control over our emotions and thoughts, deepen self-understanding, clarify our goals. Raising our vibrational frequency to realize our dreams while meeting new challenges and difficulties with greater composure.${
  isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' }) ? `
## 21-Day System Course (Next Step)
- This is a 21-day immersive journey, 5 classes per week, each 60–90 minutes. It’s Teacher Tang Ning’s signature beginner course. This time, students get lifetime replays (normally just one year)—amazing value!
- Perfect for beginners. The official course covers 12 traditional meditation methods, teaching the three keys: posture, breath, and thought.
  - Three Zen Methods: Sitting, standing, lying meditation—cultivates awareness, releases negative energy, solves "can’t find the right feeling" issues.
  - Five Elemental Breaths: Earth, fire, water, wind, space. Builds energy and corrects shallow, tense, or restricted breathing.
  - Four Awareness Meditations: Sound, light, breath, thought. For advanced practice and high-frequency growth.
- Price: Original price 499$ , now 299$ (limited time offer), including exclusive gifts: Tang Ning’s meditation cushion, a 3-month Moer App pass (100+ meditation audios), and summary notes. It’s the best value among the 40+ courses offered.
- Smart live classes with lifetime replays: 4 sessions a week plus live Q&A. Flexible study, 1-on-1 support.
  - Structured Practice: Weekend "practice check-ins", 40-60 minutes daily to help you bring meditation into real life.
  - Personalized Support: 1-on-1 assistant follows your progress, answers questions, and gives guidance anytime.
  - Community Learning: Supportive peers for mutual growth—no one practices alone.
- Next 21-Day Course starts: ${systemCourseStartTime}` : ''}`
  }

  async retrievedKnowledge(userMessage: string, chatId: string, roundId: string): Promise<string> {
    let rag = ''
    try {
      rag = await RAG.search(userMessage, chatId, roundId)
      logger.trace({ chat_id: chatId }, 'rag:', rag)
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }
    return rag
  }

  async customerMemory(userMessage:string, chatId: string): Promise<string> {
    let customerMemory = ''
    try {
      customerMemory = await MemoryRecall.memoryRecall(userMessage, chatId)
      logger.trace({ chat_id: chatId }, 'customerMemory:', customerMemory)
    } catch (e) {
      logger.error('Memory 查询失败', e)
    }
    return customerMemory
  }

  async customerBehavior(chatId: string): Promise<string> {
    let customerBehavior = ''
    let courseCompletionCount = 0
    const currentTime = await DataService.getCurrentTime(chatId)
    const courses = [
      { day: 0, label: '小课堂海浪冥想' },
      { day: 1, label: '第一课情绪减压' },
      { day: 2, label: '第二课财富唤醒' },
      { day: 3, label: '第三课红靴子' },
      { day: 4, label: '加播课蓝鹰预演' }
    ]
    for (const course of courses) {
      if (await DataService.isCompletedCourse(chatId, { day: course.day }) || await DataService.isCompletedCourse(chatId, { day: course.day, is_recording: true })) {
        courseCompletionCount++
        customerBehavior += `\n- ${course.label}（已完成）`
      } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: course.day, time: '20:00:00' })) {
        customerBehavior += `\n- ${course.label}（未开始）`
      } else {
        customerBehavior += `\n- ${course.label}（未完成）`
      }
    }
    const completionMessages = [
      '未完成任何课程',
      '完成了小部分课程',
      '完成了部分课程',
      '完成了大部分课程',
      '完成了大部分课程',
      '完成了所有课程'
    ]
    customerBehavior += `\n- ${completionMessages[courseCompletionCount]}`
    return customerBehavior.trim()
  }

  async customerPortrait(chatId: string): Promise<string> {
    const DEFAULT_UNKNOWN = '未知'
    const DEFAULT_USER_SLOTS: Record<string, Record<string, string>> = {
      '购买意向': {
        '系统班': DEFAULT_UNKNOWN
      }
    }
    const chatState = await chatStateStoreClient.get(chatId)
    const userSlots = UserSlots.fromRecord(chatState?.userSlots ?? {})

    // 填充缺失字段
    for (const [topic, subTopics] of Object.entries(DEFAULT_USER_SLOTS)) {
      for (const [subTopic, defaultValue] of Object.entries(subTopics)) {
        if (!userSlots.isTopicSubTopicExist(topic, subTopic)) {
          userSlots.add(new UserSlot(topic, subTopic, defaultValue, 0))
        }
      }
    }
    return userSlots.toString()
  }

  async temporalInformation(chatId: string): Promise<string> {
    const currentTime = `- Current time: ${DateHelper.getFormattedDate(new Date(), true)}`
    const timeOfDay = DateHelper.getTimeOfDay(new Date())
    const todayCourse = await DataService.getTodayCourse(chatId)
    const courseStartTime: Date | undefined = await DataService.getCourseStartTime(chatId)
    const courseTime = `\n- Course times: ${DateHelper.getFormattedDate(courseStartTime, false)} every evening at 8:00pm.`
    return `${currentTime}，${timeOfDay}，${todayCourse}${courseTime}`
  }
}