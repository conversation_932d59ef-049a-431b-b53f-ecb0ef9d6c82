import express from 'express'
import { initConfig } from './init'
import { exec } from 'child_process'
import logger from 'model/logger/logger'
import { ClientAccountConfig } from 'service/database/config'
import { Event } from 'service/message_handler/ycloud/message_sender'
import { YCloudMessageHandler } from 'service/message_handler/ycloud/message_handler'
import { WorkFlow } from '../workflow/workflow'
import { handleUnknownMessage, handleImageMessage, handleVideoMessage } from './message_handler'
import { SendWelcomeMessageTask } from './handler/task/send_welcome_message_task'
import { IMoerEvent, MoerEventHandler } from './handler/event_handler'
import { messageReplyServiceClient } from '../service/message_reply_service_instance'
import { chatDBCommonClient, chatHistoryServiceClient, chatStateStoreClient } from '../service/base_instance'


const messageHandler = new YCloudMessageHandler({
  handleUnknownMessage,
  handleImageMessage,
  handleVideoMessage,
  sendWelcomeMessage: SendWelcomeMessageTask.sendWelcomeMessage.bind(SendWelcomeMessageTask),
  workflow:WorkFlow
}, chatDBCommonClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient)

export interface ITimeConfig {
  chatId: string
  jumpTime: string
  isStartPush?: boolean // 是否开始主动发送营销信息
  status?: {
    is_complete_pre_course?: boolean // 是否完成小讲堂
    is_complete_day1_course?: boolean
    is_complete_day1_course_recording?: boolean
    is_complete_day2_course?: boolean
    is_complete_day2_course_recording?: boolean
    is_complete_day3_course?: boolean
    is_complete_day3_course_recording?: boolean
    is_complete_day4_course?: boolean
    is_complete_day4_course_recording?: boolean
  }
}

interface IClearCache {
  chatId: string
}

export interface ITestEvent {
  chatId: string
  name: string // 事件名称
  [key: string]: any
}

const app = express()
app.use(express.json())
app.get('/', (req, res) => {
  logger.log('Hello Client, this is Server!')
  res.send('Hello Client!')
})

app.post('/message', (req, res) => {
  const msg:Event = req.body
  logger.log(JSON.stringify(msg, null, 2))
  if (msg.type == 'whatsapp.inbound_message.received' && msg.whatsappInboundMessage) {
    messageHandler.handle(msg.whatsappInboundMessage)
  } else {
    logger.error(`接受到非收到的消息类型, message: ${JSON.stringify(msg)}`)
  }
  res.status(200).send('ok')
})

// 墨尔事件 webhook
app.post('/moer/event', async (req, res) => {
  const data: IMoerEvent = req.body

  MoerEventHandler.handle(data) // 处理 Moer 事件

  res.send({
    code: 200,
    msg: 'ok'
  })
})

/**
 * 测试接口
 */
app.post('/test/timeConfig', async (req, res) => {
  const data = req.body as ITimeConfig
  // TestHandler.handleTimeConfig(data)
  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/clear_cache', async(req, res) => {
  const data = req.body as IClearCache

  // 强制从数据库重新读取状态
  await chatStateStoreClient.clearCache(data.chatId)

  res.send({
    code: 200,
    msg: 'ok'
  })
})

app.post('/test/event', async (req, res) => {
  const data = req.body as ITestEvent
  // TestHandler.handleEvent(data)
  res.send({
    code: 200,
    msg: 'ok'
  })
})

catchGlobalError() // 防止 抛错，导致服务停止
initServer()

async function initServer() {
  await initConfig()

  messageHandler.startWorker()


  app.listen(8001, '0.0.0.0', () => {
    console.log(`Server is running on port ${8001}`)
  })
}


function catchGlobalError() {
  // 捕获未捕获的同步异常
  process.on('uncaughtException', (err) => {
    logger.error(err, 'Uncaught Exception')
  })

  // 捕获未处理的 Promise 拒绝
  process.on('unhandledRejection', (reason, promise) => {
    logger.error({ promise, reason }, 'Unhandled Rejection at: Promise')
  })
}