import { ClientAccountConfig, loadConfigByAccountName } from 'service/database/config'
import { Config, MoerAccountType } from 'config'
import chalk from 'chalk'
import { ObjectUtil } from 'lib/object'
import { TaskRegister } from '../schedule/task_register'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { moerOverseasVisualizedSopProcessor } from '../service/extract_instance'

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = 'Ms. Mai'
  Config.setting.projectName = 'moer_overseas'

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  moerOverseasVisualizedSopProcessor.start()
}

