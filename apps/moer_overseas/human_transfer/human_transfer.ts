import { eventTrackClient, humanTransferClient } from '../service/instance'
import { GroupNotification } from 'service/group_notification/group_notification'
import { HumanTransferType, NotificationProvider, NotifyOptions } from 'service/human_transfer/human_transfer'
import { IEventType } from 'model/logger/data_driven'
import { ObjectUtil } from 'lib/object'

export class HumanTransfer {
  public static async transfer(chatId: string, userId: string, transferMessage: string, toHuman: boolean |'onlyNotify' = true, additionalMsg?: string) {
    if (transferMessage !== HumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的 日志在上级进行处理，这里不进行重复处理
      eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, transferMessage) })
    }
    const message = `${ transferMessage }，${ additionalMsg ? `\n${ additionalMsg }` : ''}`
    await humanTransferClient.transferWithMessage(chatId, userId, message, toHuman)
  }
}
