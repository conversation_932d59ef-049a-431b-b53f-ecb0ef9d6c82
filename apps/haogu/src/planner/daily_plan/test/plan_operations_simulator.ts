import { PlanOperations } from '../../types'

/**
 * 任务项接口
 */
export interface TaskItem {
  id: string
  description: string
  time: string // e.g., "15:00", "2025-09-05T21:35:00Z", "待定"
  type?: 'fixed_time' | 'flexible' | string | undefined
}

/**
 * 计划操作模拟器的返回结果类型
 */
export type SimulationResult = {
  before: TaskItem[]
  after: TaskItem[]
  operations: {
    added: TaskItem[]
    updated: Array<{ before: TaskItem, after: TaskItem }>
    removed: TaskItem[]
    merged: Array<{
      from: TaskItem[],
      into: TaskItem,
      result: TaskItem
    }>
  }
}

/**
 * 计划操作模拟器
 * 用于展示计划操作前后的对比，不实际操作数据库
 */
export class PlanOperationsSimulator {

  public static simulateOperations(
    originalTasks: TaskItem[],
    planOperations: PlanOperations
  ): SimulationResult {
    // 深拷贝原始任务列表，避免修改原始数据
    let resultTasks = JSON.parse(JSON.stringify(originalTasks)) as TaskItem[]

    // 记录操作详情
    const operations: SimulationResult['operations'] = {
      added: [],
      updated: [],
      removed: [],
      merged: []
    }

    // 1. 处理合并操作
    if (planOperations.toMerge && planOperations.toMerge.length > 0) {
      const idsToRemoveAfterMerge = new Set<string>()

      for (const mergeOp of planOperations.toMerge) {
        const { from, into, mergedContent } = mergeOp

        const fromTasks = from
          .map((id) => originalTasks.find((t) => t.id === id))
          .filter((t): t is TaskItem => Boolean(t))

        const intoTaskInResult = resultTasks.find((t) => t.id === into)

        if (intoTaskInResult && fromTasks.length > 0) {
          const originalIntoTask = JSON.parse(JSON.stringify(intoTaskInResult))

          intoTaskInResult.description = mergedContent
          if (mergeOp.type) {
            intoTaskInResult.type = mergeOp.type
          }

          operations.merged.push({
            from: fromTasks,
            into: originalIntoTask,
            result: JSON.parse(JSON.stringify(intoTaskInResult))
          })

          from.forEach((id) => {
            if (id !== into) {
              idsToRemoveAfterMerge.add(id)
            }
          })
        }
      }

      const removedFromMerge = resultTasks.filter((task) => idsToRemoveAfterMerge.has(task.id))
      operations.removed.push(...JSON.parse(JSON.stringify(removedFromMerge)))
      resultTasks = resultTasks.filter((task) => !idsToRemoveAfterMerge.has(task.id))
    }

    // 2. 处理删除操作
    if (planOperations.toRemove && planOperations.toRemove.length > 0) {
      const idsToRemove = new Set(planOperations.toRemove)
      const tasksToRemove = resultTasks.filter((task) => idsToRemove.has(task.id))
      operations.removed.push(...JSON.parse(JSON.stringify(tasksToRemove)))
      resultTasks = resultTasks.filter((task) => !idsToRemove.has(task.id))
    }

    // 3. 处理更新操作
    if (planOperations.toUpdate && planOperations.toUpdate.length > 0) {
      for (const updateItem of planOperations.toUpdate) {
        const taskToUpdate = resultTasks.find((t) => t.id === updateItem.id)
        if (taskToUpdate) {
          const originalTaskFromBefore = originalTasks.find((t) => t.id === updateItem.id)

          operations.updated.push({
            before: originalTaskFromBefore || JSON.parse(JSON.stringify(taskToUpdate)),
            after: { ...taskToUpdate, description: updateItem.content }
          })

          taskToUpdate.description = updateItem.content
          if (updateItem.type) {
            taskToUpdate.type = updateItem.type
          }
        }
      }
    }

    // 4. 处理新增操作
    if (planOperations.toAdd && planOperations.toAdd.length > 0) {
      const toAddArray = Array.isArray(planOperations.toAdd) ? planOperations.toAdd : []
      for (const addItem of toAddArray) {
        let newTask: TaskItem
        const newId = `${originalTasks.length + operations.added.length + 1}`
        if (typeof addItem === 'string') {
          newTask = {
            id: newId,
            description: addItem,
            time: '待定',
            type: 'flexible'
          }
        } else {
          newTask = {
            id: newId,
            description: addItem.content,
            time: addItem.send_time,
            type: addItem.type || 'flexible'
          }
        }
        resultTasks.push(newTask)
        operations.added.push(newTask)
      }
    }

    // 最终重新分配ID，确保列表是连续的
    resultTasks = resultTasks.map((task, index) => ({ ...task, id: `${index + 1}` }))

    // 更新操作记录中的after ID
    operations.updated.forEach((op) => {
      const finalTask = resultTasks.find((t) => t.description === op.after.description)
      if (finalTask) op.after.id = finalTask.id
    })
    operations.merged.forEach((op) => {
      const finalTask = resultTasks.find((t) => t.description === op.result.description)
      if (finalTask) op.result.id = finalTask.id
    })
    operations.added.forEach((op) => {
      const finalTask = resultTasks.find((t) => t.description === op.description)
      if (finalTask) op.id = finalTask.id
    })

    return {
      before: originalTasks,
      after: resultTasks,
      operations
    }
  }

  /**
   * 格式化并可视化显示修改后的当天任务，并按时间排序
   * @param result 模拟结果
   * @returns 格式化的字符串
   */
  public static formatUpdatedTasksVisual(result: SimulationResult): string {
    let output = '✨ **今日计划已更新 (按时间排序)** ✨\n\n'

    const addedIds = new Set(result.operations.added.map((t) => t.id))
    const updatedMap = new Map(result.operations.updated.map((u) => [u.after.id, u.before]))
    const mergedMap = new Map(result.operations.merged.map((m) => [m.result.id, m]))

    const sortedAfterTasks = [...result.after].sort((a, b) => {
      // 辅助函数，尝试将时间字符串（如 "15:00"）转换为今天的日期对象
      const toDate = (timeStr: string): Date | null => {
        // 正则匹配 HH:mm 格式
        const match = timeStr.match(/^(\d{1,2}):(\d{2})$/)
        if (match) {
          const now = new Date()
          return new Date(now.getFullYear(), now.getMonth(), now.getDate(), parseInt(match[1], 10), parseInt(match[2], 10))
        }
        // 尝试直接解析为日期
        const d = new Date(timeStr)
        if (!isNaN(d.getTime())) {
          return d
        }
        return null
      }

      const dateA = toDate(a.time)
      const dateB = toDate(b.time)

      const isAValid = dateA !== null
      const isBValid = dateB !== null

      if (isAValid && !isBValid) return -1
      if (!isAValid && isBValid) return 1
      if (!isAValid && !isBValid) return 0

      return dateA!.getTime() - dateB!.getTime()
    })

    output += '📋 **最终任务列表:**\n'
    sortedAfterTasks.forEach((task) => {
      const taskId = task.id
      if (addedIds.has(taskId)) {
        output += `➕ ${taskId}. ${task.description} (${task.time}) \`[新增]\`\n`
      } else if (updatedMap.has(taskId)) {
        const beforeTask = updatedMap.get(taskId)!
        output += `✏️ ${taskId}. ${beforeTask.description} → **${task.description}** (${task.time}) \`[修改]\`\n`
      } else if (mergedMap.has(taskId)) {
        const mergeInfo = mergedMap.get(taskId)!
        const fromIds = mergeInfo.from.map((t) => `#${t.id}`).join(', ')
        output += `🔗 ${taskId}. ${task.description} (${task.time}) \`[合并自: ${fromIds}]\`\n`
      } else {
        output += `➡️ ${taskId}. ${task.description} (${task.time})\n`
      }
    })

    if (result.operations.removed.length > 0) {
      output += '\n❌ **已移除的任务:**\n'
      result.operations.removed.forEach((task) => {
        output += `  - \`[原ID: ${task.id}]\` ${task.description}\n`
      })
    }

    output += '\n'
    output += `📊 **任务统计:** 操作前 ${result.before.length} 项 → 操作后 ${result.after.length} 项\n`

    return output
  }
}