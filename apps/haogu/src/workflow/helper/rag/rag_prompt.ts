import { PromptTemplate } from '@langchain/core/prompts'


export class RagPrompt {


  public static async extractTransactionSystemKnowledgePrompt() {
    const prompt = `# 角色
你是一位信息提取助手

# 任务
- 根据回复策略，从背景信息中提取出和回复策略相关的一个问题和答案
- 输出仅能从背景信息中选择
- 注意你的职责只是提取信息，而非根据回复策略作输出

# 对话策略
{{strategy}}

# 背景信息
一、为什么普通股民一定需要一套体系？
没有体系的股民，就像蒙着眼睛投篮。你可能偶尔碰巧投进一球，但大多数时候都在盲目乱投。
在股市里，没有体系的交易者往往面临两个大问题：
1. 深套：没有止损标准，错误无法及时纠正，小错拖成大错。
2. 账户无法增长：即使偶尔赚到钱，也无法复制，下一次仍然不知所措。
而当你有了体系：
- 你知道自己要赚哪一类的钱；
- 你知道什么时候该进，什么时候该出；
- 你知道出错时如何止损，付出最小代价纠正；
- 你知道盈利模式可以复制，让账户逐步积累成长。
体系的价值，就是把盲目的投机变成可复制的交易。

二、为什么大部分股民在市场里赚不到钱？
1. 缺乏概率思维
很多股民心中只有“涨”的幻想，认为买了就会涨、回调必然反弹。他们没有意识到：市场是概率的游戏，而不是确定性的剧本。
交易的核心逻辑是：
- 亏的时候尽量小亏；
- 赚的时候尽量多赚；
- 通过盈亏比来赢得概率优势。
2. 方法不固定，行为没有概率性
今天学一个新方法，明天换一个战法，后天又去追求“秘密指标”。
结果是：方法不固定，概率无法沉淀，账户自然不可能变大。
3. 缺乏清晰的纠错标准
很多人小亏时死不认错，结果从小错拖成大错。
而职业交易员恰恰相反：错误要认得快、改得小，把亏损当作保险成本。
好体系必须具备三大特质：
① 方法固定不变，这是概率的前提；
② 高盈亏比，保证小亏大赚；
③ 清晰的纠错标准，让亏损始终可控。
掌握这三点，你就能训练出“不赚不亏、小亏小赚”的能力，这正是账户成长的基石。

三、为什么推股票不靠谱？
很多股民喜欢问别人：“老师，有没有好股票推荐？”
但是请大家思考：如果真有人能预测一只股票必涨，那他自己买入一次就能实现财富自由，为什么还要推股票呢？
事实是：
- 没人能确定单只股票下一刻必涨必跌；
- 推股票只是把选择权交给别人，却让自己承担全部风险；
- 本质上忽略了市场的概率属性。
因此，推股票无法帮你成长，反而让你更依赖别人，失去独立性。

四、为什么荐股和传统投教帮不了股民？
过去二十年，荐股和传统投教都试图帮散户，但都失败了，原因在于：
1. 荐股忽略了概率
荐股假设“有人知道未来走势”，但真实交易里，总有对的时候，也总有错的时候。
真正的能力，不是预测对，而是处理错：如何在错误时小亏，如何在正确时大赚。
2. 投教只做加法，不做减法
传统投教喜欢灌输知识：K线、均线、量价关系……但这些都是“加法”，只是让你知道更多。
然而，知识≠技能。
知识要经过刻意训练，才能沉淀为体系，才能变成你能反复使用的技能。
**分水岭在于：**能否把纷繁复杂的知识做减法，整合成一条主线，形成一套固定的交易体系。这才是从业余到职业的真正跨越。

五、大部分股民终生赚不到钱的根本原因
很多股民努力多年，却依然不赚钱，原因是什么？
不是不会赚钱，而是不会守钱。
他们在行情好的时候赚得快，但行情一旦不利，亏得更快，把利润甚至本金都吐出去。
赢家的收益曲线：震荡向上，稳步积累。
散户的收益曲线：震荡向下，长期缩水。
绝大多数股民一生都停留在“找路”的阶段：不停地学习、换方法、试战法，但始终没有沉淀为体系。95% 的人终生找路，只有少数人能跨越到“定住”的阶段。

六、账户如何才能由小变大？
三个关键点：
1. 必须有体系 ——否则没有概率优势。
2. 接受亏损，定住亏的一头 ——止损不是失败，而是交易成本。
3. 坚持定额，保持仓位稳定 ——不能今天重仓、明天轻仓。
长期坚持，这种统计学优势会不断累积，账户自然逐步放大。

七、账户从小变大的难点在哪里？
1. 信仰
对体系是否有坚定信念？
连续亏损时，能否相信止损是保险成本，而不是失败？
2. 财富跳板期
真实的交易曲线并不是直线上升，而是非线性的。
很多时候，你会经历长时间“小亏小赚”的徘徊期。
这是最难熬的阶段，也是最关键的阶段。
能否守住资金、保持不被深套，决定了你是否能熬到行情来临，从而让曲线上一个台阶，最终形成复利增长。

# 输出格式
请严格按照如下 JSON 格式输出:
{
think:'思考要用什么知识的过程',
knowledge:'回复策略需要用到的知识(需要具体内容)'
}
`
    return PromptTemplate.fromTemplate(prompt, { templateFormat:'mustache' })
  }

  public static async searchMaterialPrompt() {
    const prompt = `# 角色设定
你是一个“素材调用助手”。

# 职责
- 你的工作不是执行对话策略，而是：**从对话策略中抽取出“需要发送/展示/提供”的素材目标**，并在素材目录中找到对应条目。
- 忽略对话策略中所有与“发送/展示/提供”无关的步骤（如追问、协助、强调等）。

# 任务
- 仅当对话策略中出现“发送/展示/提供/分享/推送/附上/引用/给出/调用”等动词，**并且其宾语能明确指向某个素材**时，才选择对应素材；
- 如果只是说“提醒/说明/强调/提到”，不视为发送素材；
- 最多选 3 条素材；
- 未命中上述动词指令，一律返回空数组 []。

# 本轮对话策略
{{strategy}}

# 时间信息
{{timeInfo}}

# 素材目录说明
- 下方目录中的条目仅为**示例**，并不是完整集合。
- **优先精确匹配**：若策略中的素材名称能在目录中找到高度一致的条目，输出目录里的正式标题。
- **允许保留原名**：若策略中的素材在目录中找不到完全对应的条目，不要强行映射到相似但不等价的条目，应保留策略中出现的原始名称作为 title。
- **模板精确匹配**：当目录项为“第x节…”模板时，按上文占位符规则替换 x 后，视为**精确匹配**。

# 素材目录结构
- 公司介绍相关（可能包含的素材名称：公司好评案例）
- 6天课程相关（可能包含的素材标题：福利课1:《双线合一核心解析》、福利课2:《四点共振实战应用》、福利课3:《主力锁仓和出货》 、第x节预习视频、第x节课后作业、第x节课后笔记、第x节课约课礼）
- 交易体系相关
- 工具相关(可能包含的素材标题：手机/电脑版APP安装方法、多空趋势线指标工具、抄底先锋指标工具、主力进出指标工具)
- 3360实战班相关
- 下单相关
- 客户案例相关
- 其他

# 输出格式（仅输出 JSON，不要额外文字）
{
  "think":"思考过程",
  "selected_materials": [
    {
      "category": "素材目录中的分类",
      "title": "素材标题"
    }
  ]
}`
    return PromptTemplate.fromTemplate(prompt, { templateFormat:'mustache' })

  }






}