import { RAG } from '../rag'
import { UUID } from 'lib/uuid/uuid'
import { chatHistoryServiceClient } from '../../../../config/instance/base_instance'
import { DataService } from '../../get_data'

describe('ragTest', () => {

  it('simpleRag', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？'
    const strategy = ''

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return inputQuery
    }

    DataService.getCurrentTime = async () => {
      return {
        day: 6,
        time: '16:00:00',
      }
    }

    const res = await RAG.search(inputQuery, strategy, [], '123', UUID.v4())

    console.log('res', res)
  }, 9e8)

  it('multiQuestionRetrieve', async () => {
    const inputQuery = '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    const strategy = ''

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return '我想问下你们这个课程多少钱啊？然后到时能不能分期？'
    }

    const res = await RAG.search(inputQuery, strategy, [], '123', UUID.v4())

    console.log('res', res)
  }, 9e8)


  it('testActionTrigger', async () => {

    const actions = ['发送相关素材']
    const inputQuery = '有约课礼吗'
    const strategy = '立即发送第一节课约课礼'

    chatHistoryServiceClient.getDialogHistory = async (chat_id: string) => {
      return inputQuery
    }

    const res = await RAG.search(inputQuery, strategy, actions, '123', UUID.v4())

    console.log('res', res)

  }, 9e8)
})
