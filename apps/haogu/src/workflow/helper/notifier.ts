import { GroupNotification } from 'service/group_notification/group_notification'
import { NotificationProvider, NotifyOptions } from 'service/human_transfer/human_transfer'

export class Notifier implements NotificationProvider {
  private static imBotId = '1688858254705213'
  private static groupId = 'R:10964085377574680'
  async notify (message: string, options?: NotifyOptions): Promise<void> {
    await GroupNotification.notify(message, options?.groupId ?? Notifier.groupId, options?.imBotId ?? Notifier.imBotId)
  }
}