import { ContentItem, ContentItemMediaType, ContentItemType } from 'model/haogu/crm/type'
import { haoguScrmApi } from '../../config/instance/api_instance'
import { PrismaMongoClient } from '../../database/prisma'
import { RAG<PERSON>elper } from 'service/rag/rag_helper'
import ElasticSearchService from 'model/elastic_search/elastic_search'
import { DataService } from './get_data'
import { IMaterial, MaterialType } from 'service/rag/material/material_manager'
import logger from 'model/logger/logger'
import { ErrorComponent } from 'model/logger/errorTypes'

const RAG_INDEX = 'haogu_material'


export class MaterialManager {

  public async searchMaterialById(sourceId: string): Promise<IMaterial | null> {
    return (await PrismaMongoClient.getInstance().material.findFirst({
      where: {
        source_id: sourceId
      }
    })) as IMaterial
  }

  public async searchMaterialByTitle(chatId: string, title: string, category: string, threadHold?: number) {
    const filterDoc = await this.getMaterialFilterDoc(chatId)

    if (!threadHold) {
      threadHold = 0.8
    }

    const filter =  {
      bool:{
        must:[
          {
            match:{
              'metadata.category':category
            }
          },
          {
            terms: {
              'metadata.doc': filterDoc
            }
          }
        ]
      }
    }

    const searchRes =  await ElasticSearchService.embeddingSearch(
      RAG_INDEX,
      title,
      1,
      threadHold,
      filter
    )

    if (searchRes.length !== 0) {
      return new MaterialManager().searchMaterialById(searchRes[0].metadata.source_id)
    }

    return null
  }

  public async searchMaterialByTitleOnly(title: string): Promise<IMaterial | null> {
    const searchRes =  await ElasticSearchService.embeddingSearch(
      RAG_INDEX,
      title,
      1,
      0.7,
    )
    return searchRes.length !== 0 ? new MaterialManager().searchMaterialById(searchRes[0].metadata.source_id) : null
  }

  public async saveMaterial(mainCategory:string, subCategory:string, doc: string, item: ContentItem) {
    const title = item.title || ''
    const description = item.desc || item.sphfeed_desc || item.content || title || ''
    await PrismaMongoClient.getInstance().material.create({
      data: {
        source_id:item.id.toString(),
        type:this.ItemTypeToMediaType(item),
        title:title,
        description:description,
        main_category:mainCategory,
        sub_category:subCategory,
        doc:doc,
        enable:false,
        es_id:'',
        data:JSON.parse(JSON.stringify(item))
      }
    })
  }

  public async deleteMaterial(id: string) {
    // 先获取素材信息（包括es_id）
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 删除ES中的数据
    if (material.es_id) {
      try {
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
      } catch (error) {
        logger.error({ component: ErrorComponent.DATA_SERVICE }, '删除ES文档失败', error)
        // 继续删除MongoDB数据，即使ES删除失败
      }
    }

    // 删除MongoDB中的数据
    await PrismaMongoClient.getInstance().material.delete({
      where: { id }
    })
  }

  public async enableMaterial(id: string) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 如果已经有es_id，说明已经在ES中，直接返回
    if (material.es_id && material.enable) {
      return
    }

    // 添加到ES
    const [esId] = await RAGHelper.addDocuments(RAG_INDEX, [{
      pageContent: material.title,
      metadata: {
        title: material.title,
        description: material.description,
        source_id: material.source_id,
        doc: material.doc,
        category: material.main_category
      }
    }])

    // 更新MongoDB中的es_id和enable状态
    await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: {
        es_id: esId,
        enable: true
      }
    })
  }

  public async disableMaterial(id: string) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 从ES中删除
    if (material.es_id) {
      try {
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
      } catch (error) {
        logger.error({ component: ErrorComponent.DATA_SERVICE }, '从ES删除文档失败', error)
        // 继续更新MongoDB，即使ES删除失败
      }
    }

    // 更新MongoDB，清空es_id并设置enable为false
    await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: {
        es_id: '',
        enable: false
      }
    })
  }

  public async updateMaterialContent(id: string, updates: {
    title?: string;
    description?: string;
    doc?: string;
    main_category?: string;
    sub_category?: string;
  }) {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 更新MongoDB
    const updatedMaterial = await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: updates
    })

    // 如果素材已启用且有es_id，同时更新ES中的数据
    if (material.enable && material.es_id) {
      try {
        // 先删除旧的ES文档
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])

        // 重新添加更新后的文档
        const [newEsId] = await RAGHelper.addDocuments(RAG_INDEX, [{
          pageContent: updates.title || material.title,
          metadata: {
            title: updates.title || material.title,
            description: updates.description || material.description,
            source_id: material.source_id,
            doc: updates.doc || material.doc,
            category: updates.main_category || material.main_category
          }
        }])

        // 更新MongoDB中的es_id
        await PrismaMongoClient.getInstance().material.update({
          where: { id },
          data: { es_id: newEsId }
        })
      } catch (error) {
        logger.error({ component: ErrorComponent.DATA_SERVICE }, '更新ES文档失败', error)
        // ES更新失败不影响MongoDB的更新
      }
    }

    return updatedMaterial
  }

  public async searchMaterialFromHaoGu(page:number, pageSize:number, type: number, mediaType?: number) {
    const res = await haoguScrmApi.contentList(page, pageSize, 2540, type, mediaType, 1)

    return res
  }


  public async isValidCourseMaterial(chatId: string, title: string) {

    const coursePattern = /第([一二三四五六1-6])节(课后作业|课后笔记)/
    const match = title.match(coursePattern)

    if (!match) {
      return true
    }

    const chineseNumbers: { [key: string]: number } = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6
    }

    let lessonNumber = chineseNumbers[match[1]]
    if (!lessonNumber) {
      // 尝试将匹配的字符作为数字解析
      const arabicNumber = parseInt(match[1], 10)
      if (arabicNumber >= 1 && arabicNumber <= 6) {
        lessonNumber = arabicNumber
      } else {
        return false
      }
    }


    return await DataService.isInCourseTimeLine(chatId, 'afterCourse', lessonNumber)

  }

  private async getMaterialFilterDoc(chatId: string) {
    if (await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 4)) {
      return ['全局', '售前']
    } else if (await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 6)) {
      return ['全局', '售中']
    } else {
      return ['全局', '售后']
    }
  }

  private ItemTypeToMediaType(item: ContentItem) {
    switch (item.type) {
      case ContentItemType.Article:
        return MaterialType.Article
      case ContentItemType.File:
        return MaterialType.File
      case ContentItemType.Link:
        return MaterialType.Link
      case ContentItemType.Poster:
        return MaterialType.Poster
      case ContentItemType.Channels:
        return MaterialType.Channels
      case ContentItemType.Text:
        return MaterialType.Text
      case ContentItemType.Media:
        switch (item.media_type) {
          case ContentItemMediaType.Image:
            return MaterialType.Image
          case ContentItemMediaType.Audio:
            return MaterialType.Audio
          case ContentItemMediaType.Video:
            return MaterialType.Video
          case ContentItemMediaType.Voice:
            return MaterialType.Voice
        }
    }

    return MaterialType.Text
  }
}