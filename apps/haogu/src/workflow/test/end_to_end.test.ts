import dayjs from 'dayjs'
import fs from 'fs'
import path from 'path'
import { DataService } from '../helper/get_data'
import { Config } from 'config'
import { when } from 'jest-when'
import { Workflow } from '../workflow'
import { chatHistoryServiceClient } from '../../config/instance/base_instance'
import { getChatId } from 'config/chat_id'

describe('Test', function () {
  beforeEach(() => {
    Config.setting.projectName = 'haogu'

    // 重置 spy 的实现，再设置默认返回（未指定 chatId 时使用）
    getCurrentTimeMock.mockReset()
    getCurrentTimeMock.mockResolvedValue({ day: 0, time: '00:00:00' })

    getCourseStartTimeMock.mockReset()
    getCourseStartTimeMock.mockResolvedValue(dayjs().add(1, 'day').hour(19).minute(20).second(0).toDate())
  })

  const getCurrentTimeMock = jest
    .spyOn(DataService, 'getCurrentTime')
    .mockResolvedValue({ day: 0, time: '00:00:00' })

  const getCourseStartTimeMock = jest
    .spyOn(DataService, 'getCourseStartTimeByChatId')
    .mockResolvedValue(dayjs().add(1, 'day').hour(19).minute(20).second(0).toDate())


  it('端到端测试', async () => {
    // 加载数据
    const cwdDatasetPath = path.join(process.cwd(), 'dev', 'end_to_end_dataset')
    const localDatasetPath = path.resolve(__dirname, '../../../dev/end_to_end_dataset')
    const datasetPath = fs.existsSync(cwdDatasetPath) ? cwdDatasetPath : localDatasetPath
    const files = fs.readdirSync(datasetPath).filter((f) => f.endsWith('.json'))
    expect(files.length).toBeGreaterThan(0)

    // 读取第一个数据文件
    const raw = fs.readFileSync(path.join(datasetPath, files[0]), 'utf8')
    const dataset: Record<string, Array<{ msg_type: 'text' | 'image'; content: string; send_time: string; role: 'user' | 'counselor' }>> = JSON.parse(raw)

    // 先取第一个客户作为测试
    const userIds = Object.keys(dataset)
    expect(userIds.length).toBeGreaterThan(0)

    const userId = userIds[0]
    const botId = '113' // 使用本地测试账号
    const test_chat_id = getChatId(userId, botId)

    const conv = dataset[userId]

    // 工具函数：解析 Dn_HH:mm 为 { day, time: HH:mm:ss }
    const parseSendTime = (s: string): { day: number; time: string } => {
      // 形如 D0_10:01
      const m = s.match(/^D(-?\d+)_([0-2]\d:[0-5]\d)$/)
      if (!m) {
        // 兜底：默认 day=0, 00:00:00
        return { day: 0, time: '00:00:00' }
      }
      const day = Number(m[1])
      const time = `${m[2]}:00`
      return { day, time }
    }

    // 计算相邻两条记录的时间间隔（秒）
    const diffSeconds = (a: string, b: string): number => {
      const ta = parseSendTime(a)
      const tb = parseSendTime(b)
      const toSec = (t: { day: number; time: string }) => {
        const [hh, mm, ss] = t.time.split(':').map((x) => Number(x))
        return t.day * 24 * 3600 + hh * 3600 + mm * 60 + ss
      }
      return Math.abs(toSec(tb) - toSec(ta))
    }

    // 将图片类型转为占位文本
    const toRenderable = (msg: { msg_type: 'text' | 'image'; content: string }): string => {
      if (msg.msg_type === 'image') {
        return `【图片Url】${msg.content}`
      }
      return msg.content
    }

    // 遍历对话
    for (let i = 0; i < conv.length; i++) {
      const msg = conv[i]
      if (msg.role === 'counselor') {
        // 销售发言，直接写入机器人消息
        await chatHistoryServiceClient.addBotMessage(test_chat_id, toRenderable(msg))
        continue
      }

      // 用户发言：向下合并，最多 3 条，间隔 < 15s
      const merged: string[] = [toRenderable(msg)]
      let lastIndex = i
      let take = 1
      while (take < 3 && lastIndex + 1 < conv.length) {
        const next = conv[lastIndex + 1]
        if (next.role !== 'user') break
        const gap = diffSeconds(conv[lastIndex].send_time, next.send_time)
        if (gap < 15) {
          merged.push(toRenderable(next))
          lastIndex += 1
          take += 1
        } else {
          break
        }
      }

      // 在触发 Workflow.step 前，mock 最后一条客户消息的时间
      const { day, time } = parseSendTime(conv[lastIndex].send_time)
      when(getCurrentTimeMock)
        .calledWith(test_chat_id)
        .mockResolvedValue({ day, time })

      // 触发回复
      await Workflow.step(test_chat_id, merged.join('\n'))

      // 跳过已合并的消息
      i = lastIndex
    }


  }, 1E8)
})
