import { HumanTransfer, HumanTransferType } from 'service/human_transfer/human_transfer'
import { chatDBClient, chatStateStoreClient } from './base_instance'
import { eventTrackClient } from './event_track_instance'
import { Notifier } from '../../workflow/helper/notifier'

export const humanTransferClient = new HumanTransfer({
  transferMessage: HumanTransferType,
  eventTracker: eventTrackClient,
  chatDB: chatDBClient,
  chatStateStore: chatStateStoreClient,
  notifier: new Notifier()
})
