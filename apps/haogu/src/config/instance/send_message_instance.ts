import { chatDBClient, chatHistoryServiceClient } from './base_instance'
import { Reply } from 'service/agent/reply'
import { PrismaMongoClient } from '../../database/prisma'
import { ChatIdTransfer } from '../chat_id_transfer'
import { PrismaMongoClient as PrismaMongoCommonClient } from 'model/mongodb/prisma'
import { MaterialManager } from '../../workflow/helper/material_manager'
import { HaoguMessageSender } from 'service/message_handler/haogu/message_sender'
import { HaoguCommonMessageSender } from 'service/visualized_sop/common_sender/haogu'
import { haoguScrmApi } from './api_instance'

export const chatIdTransfer = new ChatIdTransfer(PrismaMongoClient.getInstance(), PrismaMongoCommonClient.getConfigInstance())
export const materialManager = new MaterialManager()
export const haoguMessageSender = new HaoguMessageSender(haoguScrmApi, chatHistoryServiceClient)
export const commonMessageSender = new HaoguCommonMessageSender(haoguMessageSender, chatHistoryServiceClient, chatIdTransfer, materialManager)

export const replyClient = new Reply(chatDBClient, chatHistoryServiceClient, commonMessageSender, new MaterialManager().searchMaterialByTitleOnly)

