import { ClientAccountConfig, loadConfigByAccountName } from 'service/database/config'
import { Config } from 'config'
import chalk from 'chalk'
import { manifest } from '../config/manifest'
import { override } from '../config/override'
import { TaskRegister } from '../workflow/helper/register_task'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { haoguVisualizedSopProcessor } from '../config/instance/instance'
import { chatDBClient, chatHistoryServiceClient, chatStateStoreClient } from '../config/instance/base_instance'
import { messageReplyServiceClient } from '../config/instance/message_replay_service_instance'
import { eventTrackClient } from '../config/instance/event_track_instance'
import { taskWorker } from '../config/instance/task_instance'
import { HaoguMessageHandler } from 'service/message_handler/haogu/message_handler'

export async function init() {
  // 初始化配置
  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = '老师'
  Config.setting.projectName = manifest.projectName

  // 注册所有任务函数
  TaskRegister.register()

  // 启动 SilentReAsk Worker
  SilentReAsk.startWorker()

  // SOP Worker
  haoguVisualizedSopProcessor.start()


  const messageHandler = new HaoguMessageHandler({
    handleUnknownMessage: override.handleUnknownMessage,
    handleImageMessage: override.handleImageMessage,
    // handleTextAndImageMessage: override.handleTextAndImageMessage,
    handleVideoMessage: override.handleVideoMessage,
    sendWelcomeMessage: override.sendWelcomeMessage
  },
  chatDBClient, chatHistoryServiceClient, chatStateStoreClient, messageReplyServiceClient, eventTrackClient
  )

  // 消息处理 Worker
  messageHandler.startWorker()

  // Planner Task Worker
  taskWorker.start()

  return {
    port: 8001,
    messageHandler,
  }
}